import pandas as pd
import pandas_ta as ta
import numpy as np
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

def calculate_wma(df, price_column='close', periods=[5, 10, 45, 65, 90]):
    """
    Calculate Weighted Moving Averagess for specified periods using pandas_ta.

    Args:
        df (pd.DataFrame): Input dataframe with price data
        price_column (str): Column name to use for WMA calculation (default: 'close')
        periods (list): List of periods for WMA calculation

    Returns:
        pd.DataFrame: DataFrame with added WMA columns
    """
    df_copy = df.copy()

    # Ensure the price column exists
    if price_column not in df_copy.columns:
        #print(f"  ✗ Price column '{price_column}' not found in dataframe")
        # Add dummy columns to avoid errors later, though processing will be invalid
        for period in periods:
             df_copy[f'WMA{period}'] = np.nan
        return df_copy

    # Calculate WMA for each period
    for period in periods:
        try:
            # Use pandas_ta to calculate WMA
            wma_values = ta.wma(df_copy[price_column], length=period).round(2)
            df_copy[f'WMA{period}'] = wma_values
            # print(f"  ✓ Calculated WMA{period}") # Keep prints for clarity during execution
        except Exception as e:
            print(f"  ✗ Error calculating WMA{period}: {str(e)}")
            df_copy[f'WMA{period}'] = np.nan

    return df_copy


def calculate_rsi_atr(df, rsi_length=14, atr_length=14, price_cols=None):
    """
    Calculate RSI and ATR indicators and add them to the DataFrame.

    Args:
        df (pd.DataFrame): Input dataframe with price data.
        rsi_length (int): Period for RSI calculation (default: 14).
        atr_length (int): Period for ATR calculation (default: 14).
        price_cols (dict or None): Optional mapping for price columns, e.g.,
            {'close': 'Close', 'high': 'High', 'low': 'Low'}
            If None, uses 'close', 'high', 'low' as column names.

    Returns:
        pd.DataFrame: DataFrame with added 'RSI_14' and 'ATR_14' columns.
    """
    df_copy = df.copy()
    # Set default column names or use provided mapping
    close_col = price_cols['close'] if price_cols and 'close' in price_cols else 'close'
    high_col = price_cols['high'] if price_cols and 'high' in price_cols else 'high'
    low_col = price_cols['low'] if price_cols and 'low' in price_cols else 'low'

    # Check if required columns exist
    if close_col in df_copy.columns:
        try:
            df_copy[f'RSI_{rsi_length}'] = ta.rsi(df_copy[close_col], length=rsi_length).round(2)
        except Exception as e:
            print(f"  ✗ Error calculating RSI({rsi_length}): {str(e)}")
            df_copy[f'RSI_{rsi_length}'] = np.nan
    else:
        df_copy[f'RSI_{rsi_length}'] = np.nan

    if all(col in df_copy.columns for col in [high_col, low_col, close_col]):
        try:
            df_copy[f'ATR_{atr_length}'] = ta.atr(
                df_copy[high_col], df_copy[low_col], df_copy[close_col], length=atr_length
            ).round(2)
        except Exception as e:
            print(f"  ✗ Error calculating ATR({atr_length}): {str(e)}")
            df_copy[f'ATR_{atr_length}'] = np.nan
    else:
        df_copy[f'ATR_{atr_length}'] = np.nan

    return df_copy


def detect_crossovers(df, fast_wma='WMA5', slow_wma='WMA10'):
    """
    Detect crossovers between two WMA series.

    Args:
        df (pd.DataFrame): DataFrame containing WMA columns
        fast_wma (str): Column name for fast WMA (default: 'WMA5')
        slow_wma (str): Column name for slow WMA (default: 'WMA10')

    Returns:
        pd.DataFrame: DataFrame with added crossover signal column
    """
    df_copy = df.copy()

    # Use standard column name for WMA5/WMA10 crossovers, custom name for others
    if fast_wma == 'WMA5' and slow_wma == 'WMA10':
        crossover_signal_name = 'crossover_signal'
    else:
        crossover_signal_name = f'crossover_signal_{fast_wma}_{slow_wma}'

    # Check if required columns exist
    if fast_wma not in df_copy.columns or slow_wma not in df_copy.columns:
        #print(f"  ✗ Required WMA columns not found for crossover detection")
        df_copy[crossover_signal_name] = 'neutral'
        return df_copy

    # Initialize crossover signal column
    df_copy[crossover_signal_name] = 'neutral'

    # Calculate crossover signals
    try:
        # Get the WMA series
        fast_series = df_copy[fast_wma]
        slow_series = df_copy[slow_wma]

        # Calculate the difference and its shift
        diff = fast_series - slow_series
        diff_prev = diff.shift(1)

        # Detect crossovers
        # Upward crossover: fast WMA crosses above slow WMA
        upward_cross = (diff > 0) & (diff_prev <= 0)

        # Downward crossover: fast WMA crosses below slow WMA
        downward_cross = (diff < 0) & (diff_prev >= 0)

        # Apply signals
        df_copy.loc[upward_cross, crossover_signal_name] = 'upward'
        df_copy.loc[downward_cross, crossover_signal_name] = 'downward'

        # Count crossovers
        upward_count = upward_cross.sum()
        downward_count = downward_cross.sum()

        # print(f"  ✓ Detected {upward_count} upward and {downward_count} downward crossovers") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting crossovers: {str(e)}")

    return df_copy


def detect_supply_demand_zones(df, crossover_column='crossover_signal'):
    """
    Detect supply and demand zones based on crossover transitions.

    Supply Zones: Price ranges between 'upward' → 'downward' crossover transitions
    - Captures the candle with highest 'high' price in the period

    Demand Zones: Price ranges between 'downward' → 'upward' crossover transitions
    - Captures the candle with lowest 'low' price in the period

    Args:
        df (pd.DataFrame): DataFrame containing crossover signals and price data
        crossover_column (str): Column name containing crossover signals

    Returns:
        pd.DataFrame: DataFrame with added supply_zone and demand_zone columns
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = [crossover_column, 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        #print(f"  ✗ Required columns not found for zone detection: {missing_columns}")
        df_copy['Zones_sup_dem'] = ''
        return df_copy

    # Initialize zone columns
    df_copy['Zones_sup_dem'] = ''

    try:
        # Get crossover signals
        signals = df_copy[crossover_column]

        # Find crossover indices
        upward_indices = df_copy[signals == 'upward'].index.tolist()
        downward_indices = df_copy[signals == 'downward'].index.tolist()

        supply_zones_count = 0
        demand_zones_count = 0

        # Detect Supply Zones (upward → downward transitions)
        # Iterate through upward crossovers
        for upward_idx in upward_indices:
            # Find the next downward crossover after this upward crossover
            next_downward_indices = [idx for idx in downward_indices if idx > upward_idx]

            if next_downward_indices:
                # Get the first downward crossover after the current upward one
                downward_idx = next_downward_indices[0]

                # Ensure the range is valid (upward_idx <= downward_idx)
                if upward_idx <= downward_idx:
                    # Find the candle with highest 'high' between upward and the next downward crossover
                    zone_data = df_copy.loc[upward_idx:downward_idx]
                    if not zone_data.empty:
                        max_high_idx = zone_data['high'].idxmax()
                        df_copy.loc[max_high_idx, 'Zones_sup_dem'] = 'Supply'
                        supply_zones_count += 1

        # Detect Demand Zones (downward → upward transitions)
        # Iterate through downward crossovers
        for downward_idx in downward_indices:
            # Find the next upward crossover after this downward crossover
            next_upward_indices = [idx for idx in upward_indices if idx > downward_idx]

            if next_upward_indices:
                # Get the first upward crossover after the current downward one
                upward_idx = next_upward_indices[0]

                # Ensure the range is valid (downward_idx <= upward_idx)
                if downward_idx <= upward_idx:
                    # Find the candle with lowest 'low' between downward and the next upward crossover
                    zone_data = df_copy.loc[downward_idx:upward_idx]
                    if not zone_data.empty:
                        min_low_idx = zone_data['low'].idxmin()
                        df_copy.loc[min_low_idx, 'Zones_sup_dem'] = 'Demand'
                        demand_zones_count += 1

        # print(f"  ✓ Detected {supply_zones_count} supply zones and {demand_zones_count} demand zones") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting supply/demand zones: {str(e)}")

    return df_copy


def validate_zones(df):
    """
    Validate supply and demand zones by adding a Zone_Status column.

    Takes a DataFrame with already identified Supply and Demand zones
    (where 'Zones_sup_dem' column contains 'Supply' or 'Demand' values)
    and adds a new column 'Zone_Status' with default value 'Valid' for each identified zone.

    Args:
        df (pd.DataFrame): DataFrame containing identified supply/demand zones

    Returns:
        pd.DataFrame: DataFrame with added Zone_Status column
    """
    df_copy = df.copy()

    # Check if required column exists
    if 'Zones_sup_dem' not in df_copy.columns:
        #print("  ✗ Required column 'Zones_sup_dem' not found for zone validation")
        df_copy['Zone_Status'] = ''
        return df_copy

    try:
        # Initialize Zone_Status column with empty values
        df_copy['Zone_Status'] = ''

        # Set 'Valid' status for identified zones
        zone_mask = (df_copy['Zones_sup_dem'] == 'Supply') | (df_copy['Zones_sup_dem'] == 'Demand')
        df_copy.loc[zone_mask, 'Zone_Status'] = 'Valid'

        # Count validated zones
        supply_zones = (df_copy['Zones_sup_dem'] == 'Supply').sum()
        demand_zones = (df_copy['Zones_sup_dem'] == 'Demand').sum()
        total_zones = supply_zones + demand_zones

        #print(f"  ✓ Validated {total_zones} zones ({supply_zones} supply, {demand_zones} demand)")

    except Exception as e:
        print(f"  ✗ Error validating zones: {str(e)}")
        df_copy['Zone_Status'] = ''

    return df_copy


def update_supply_zone_status(df):
    """
    Update the status of Supply zones based on price action validation.

    For each Supply zone with 'Valid' status:
    - Checks if any subsequent candle has a high price greater than the high price of the supply zone
    - If found, marks the zone as 'Invalid' in the 'Zone_Status' column
    - If still 'Valid', performs a test check based on crossover signals
    - If test condition is met, marks the zone as 'Tested' in the 'Zone_Status' column

    Args:
        df (pd.DataFrame): DataFrame with validated zones containing 'Zone_Status' column

    Returns:
        pd.DataFrame: DataFrame with updated Supply zone statuses
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        #print(f"  ✗ Required columns not found for supply zone status update: {missing_columns}")
        return df_copy

    try:
        # Get supply zones with 'Valid' status
        # supply_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Supply') &
        #                      (df_copy['Zone_Status'] == 'Valid' or df_copy['Zone_Status'] == 'Tested')]
        supply_zones = df_copy[((df_copy['Zones_sup_dem'] == 'Supply') & (df_copy['Zone_Status'] == 'Valid'))
                               | ((df_copy['Zones_sup_dem'] == 'Supply') & (df_copy['Zone_Status'] == 'Tested'))]
        if supply_zones.empty:
            #print("  → No valid supply zones found for status update")
            return df_copy

        invalid_count = 0
        tested_count = 0

        for zone_idx in supply_zones.index:
            zone_high = df_copy.loc[zone_idx, 'high']
            zone_low = df_copy.loc[zone_idx, 'low']
            zone_crossover = df_copy.loc[zone_idx, 'crossover_signal']

            # Check subsequent candles (after the zone)
            subsequent_data = df_copy.loc[zone_idx + 1:]

            if subsequent_data.empty:
                continue

            # Check for invalidation: any subsequent high > zone high
            invalidation_check = subsequent_data['high'] > zone_high
            if invalidation_check.any():
                df_copy.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                invalid_count += 1
                continue

            # Test check logic
            test_triggered = False

            if 'downward' in zone_crossover:
                # Zone itself has downward signal - check if any subsequent high >= zone low
                test_check = subsequent_data['high'] >= zone_low
                if test_check.any():
                    test_triggered = True
            else:
                # Find next downward crossover after the zone
                downward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'downward']
                if not downward_signals.empty:
                    next_downward_idx = downward_signals.index[0]
                    # Check candles after the next downward signal
                    after_downward = df_copy.loc[next_downward_idx + 1:]
                    if not after_downward.empty:
                        test_check = after_downward['high'] >= zone_low
                        if test_check.any():
                            test_triggered = True

            if test_triggered:
                df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
                tested_count += 1

        #print(f"  ✓ Updated supply zones: {invalid_count} invalid, {tested_count} tested")

    except Exception as e:
        print(f"  ✗ Error updating supply zone status: {str(e)}")

    return df_copy


def update_demand_zone_status(df):
    """
    Update the status of Demand zones based on price action validation.

    For each Demand zone with 'Valid' status:
    - Checks if any subsequent candle has a low price less than the low price of the demand zone
    - If found, marks the zone as 'Invalid' in the 'Zone_Status' column
    - If still 'Valid', performs a test check based on crossover signals
    - If test condition is met, marks the zone as 'Tested' in the 'Zone_Status' column

    Args:
        df (pd.DataFrame): DataFrame with validated zones containing 'Zone_Status' column

    Returns:
        pd.DataFrame: DataFrame with updated Demand zone statuses
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        #print(f"  ✗ Required columns not found for demand zone status update: {missing_columns}")
        return df_copy

    try:
        # Get demand zones with 'Valid' status
        #demand_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Demand') &
        #                      (df_copy['Zone_Status'] == 'Valid' or df_copy['Zone_Status'] == 'Tested')]
        demand_zones = df_copy[((df_copy['Zones_sup_dem'] == 'Demand') & (df_copy['Zone_Status'] == 'Valid'))
                               | ((df_copy['Zones_sup_dem'] == 'Demand') & (df_copy['Zone_Status'] == 'Tested'))]
        if demand_zones.empty:
            #print("  → No valid demand zones found for status update")
            return df_copy

        invalid_count = 0
        tested_count = 0

        for zone_idx in demand_zones.index:
            zone_high = df_copy.loc[zone_idx, 'high']
            zone_low = df_copy.loc[zone_idx, 'low']
            zone_crossover = df_copy.loc[zone_idx, 'crossover_signal']

            # Check subsequent candles (after the zone)
            subsequent_data = df_copy.loc[zone_idx + 1:]

            if subsequent_data.empty:
                continue

            # Check for invalidation: any subsequent low < zone low
            invalidation_check = subsequent_data['low'] < zone_low
            if invalidation_check.any():
                df_copy.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                invalid_count += 1
                continue

            # Test check logic
            test_triggered = False

            if 'upward' in zone_crossover:
                # Zone itself has upward signal - check if any subsequent low <= zone high
                test_check = subsequent_data['low'] <= zone_high
                if test_check.any():
                    test_triggered = True
            else:
                # Find next upward crossover after the zone
                upward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'upward']
                if not upward_signals.empty:
                    next_upward_idx = upward_signals.index[0]
                    # Check candles after the next upward signal
                    after_upward = df_copy.loc[next_upward_idx + 1:]
                    if not after_upward.empty:
                        test_check = after_upward['low'] <= zone_high
                        if test_check.any():
                            test_triggered = True

            if test_triggered:
                df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
                tested_count += 1

        #print(f"  ✓ Updated demand zones: {invalid_count} invalid, {tested_count} tested")

    except Exception as e:
        print(f"  ✗ Error updating demand zone status: {str(e)}")

    return df_copy

def identify_strong_zones(df):
    """
    Identifies strong upward and downward trading zones based on WMA crossover analysis.

    Adds a 'strong_zone' column to the DataFrame:
      - 'strong_upward_zone' for strong upward zones
      - 'strong_downward_zone' for strong downward zones
      - '' (empty string) otherwise

    Returns:
        pd.DataFrame: DataFrame with the 'strong_zone' column added.
    """
    df_copy = df.copy()

    # Ensure required columns exist
    required_wma_cols = ['WMA5', 'WMA10', 'WMA45', 'WMA65', 'WMA90']
    required_cols = required_wma_cols + ['crossover_signal', 'high', 'low', 'Zones_sup_dem']
    missing_cols = [col for col in required_cols if col not in df_copy.columns]
    if missing_cols:
        df_copy['strong_zone'] = ''
        return df_copy

    # Initialize the strong_zone column
    df_copy['strong_zone'] = ''

    # Helper to find most recent zone before a given index
    def find_most_recent_zone(df, zone_type, before_idx):
        zones = df.loc[:before_idx - 1]
        candidates = zones[zones['Zones_sup_dem'] == zone_type]
        if not candidates.empty:
            return candidates.index[-1]
        return None

    # --- Strong Upward Zone Detection ---
    upward_cross_indices = df_copy[df_copy['crossover_signal'] == 'upward'].index.tolist()
    downward_cross_indices = df_copy[df_copy['crossover_signal'] == 'downward'].index.tolist()

    for up_idx in upward_cross_indices:
        prev_idx = up_idx - 1
        if prev_idx < 0:
            continue
        # Strict ascending order: WMA5 < WMA10 < WMA45 < WMA65 < WMA90
        wmas = df_copy.loc[prev_idx, required_wma_cols]
        if wmas.isnull().any():
            continue
        if not (wmas['WMA5'] < wmas['WMA10'] < wmas['WMA45'] < wmas['WMA65'] < wmas['WMA90']):
            continue

        # Find next downward crossover after up_idx
        next_downs = [idx for idx in downward_cross_indices if idx > up_idx]
        end_idx = next_downs[0] if next_downs else df_copy.index[-1]
        zone_range = df_copy.loc[up_idx:end_idx]
        # Any candle in this range with high >= WMA45?
        if ((zone_range['high'] >= zone_range['WMA45']).any()):
            # Find most recent Demand zone before up_idx
            demand_idx = find_most_recent_zone(df_copy, 'Demand', up_idx)
            if demand_idx is not None:
                df_copy.at[demand_idx, 'strong_zone'] = 'strong_upward_zone'

    # --- Strong Downward Zone Detection ---
    for down_idx in downward_cross_indices:
        prev_idx = down_idx - 1
        if prev_idx < 0:
            continue
        # Strict descending order: WMA5 > WMA10 > WMA45 > WMA65 > WMA90
        wmas = df_copy.loc[prev_idx, required_wma_cols]
        if wmas.isnull().any():
            continue
        if not (wmas['WMA5'] > wmas['WMA10'] > wmas['WMA45'] > wmas['WMA65'] > wmas['WMA90']):
            continue

        # Find next upward crossover after down_idx
        next_ups = [idx for idx in upward_cross_indices if idx > down_idx]
        end_idx = next_ups[0] if next_ups else df_copy.index[-1]
        zone_range = df_copy.loc[down_idx:end_idx]
        # Any candle in this range with low <= WMA45?
        if ((zone_range['low'] <= zone_range['WMA45']).any()):
            # Find most recent Supply zone before down_idx
            supply_idx = find_most_recent_zone(df_copy, 'Supply', down_idx)
            if supply_idx is not None:
                df_copy.at[supply_idx, 'strong_zone'] = 'strong_downward_zone'

    return df_copy


def get_resistant_zones(df, price_value, zone_type='Both'):
    """
    Identifies valid supply or demand zones based on a price value.
    
    Args:
        df (pd.DataFrame): DataFrame that has already been processed with supply/demand zones
        price_value (float): Numeric value representing the current price to check against zones
        zone_type (str): Type of zones to find ('Supply', 'Demand', or 'Both')
        
    Returns:
        pd.DataFrame: Filtered DataFrame containing only the rows that meet the conditions
    """
    df_copy = df.copy()
    
    # Check if required columns exist
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]
    
    if missing_columns or df_copy.empty:
        #print(f"  ✗ Required columns not found for resistant zones: {missing_columns}")
        return pd.DataFrame()
    
    try:
        # Initialize empty DataFrame for results
        result_df = pd.DataFrame()
        
        # Filter for Demand zones
        if zone_type in ['Demand', 'Both']:
            demand_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Demand') & 
                                  (df_copy['Zone_Status'].isin(['Valid', 'Tested'])) &
                                  (price_value <= df_copy['high']) & 
                                  (price_value > df_copy['low'])]
            result_df = pd.concat([result_df, demand_zones])
        
        # Filter for Supply zones
        if zone_type in ['Supply', 'Both']:
            supply_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Supply') & 
                                  (df_copy['Zone_Status'].isin(['Valid', 'Tested'])) &
                                  (price_value >= df_copy['low']) & 
                                  (price_value < df_copy['high'])]
            result_df = pd.concat([result_df, supply_zones])
        
        return result_df
    
    except Exception as e:
        print(f"  ✗ Error identifying resistant zones: {str(e)}")
        return pd.DataFrame()


def process_dataframe(df):
    """
    Processes a DataFrame with WMA calculations, crossover, zone detection, and zone status tracking.

    Args:
        df (pd.DataFrame): Input dataframe with price data.

    Returns:
        pd.DataFrame: Processed DataFrame with zone status tracking.
    """
    if df is None or df.empty:
        #print("  ✗ Input DataFrame is empty or None.")
        return None

    #print(f"  → Processing DataFrame with {len(df)} rows...")

    # Calculate WMAs
    df_processed = calculate_wma(df)

    #Calculate RSI and ATR
    df_processed = calculate_rsi_atr(df_processed)

    # Detect crossovers
    df_processed = detect_crossovers(df_processed)

    # Detect supply and demand zones
    df_processed = detect_supply_demand_zones(df_processed)

    # Validate zones and track their status
    df_processed = validate_zones(df_processed)
    df_processed = update_supply_zone_status(df_processed)
    df_processed = update_demand_zone_status(df_processed)

    # Identify strong zones
    df_processed = identify_strong_zones(df_processed)

    #print("  ✓ DataFrame processing complete.")
    return df_processed