#!/usr/bin/env python3
"""
Test script to verify the identify_strong_zones function works correctly.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the utils directory to the path so we can import the functions
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from processing_functions import process_dataframe

def create_test_data():
    """
    Create test data with known patterns to verify strong zone detection.
    """
    # Create a DataFrame with test data
    dates = pd.date_range('2025-01-01 09:15:00', periods=100, freq='1min')
    
    # Create base price data
    np.random.seed(42)  # For reproducible results
    base_price = 100
    prices = []
    
    for i in range(100):
        # Create some price movement
        price = base_price + np.sin(i * 0.1) * 10 + np.random.normal(0, 1)
        prices.append(price)
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p + np.random.uniform(0, 2) for p in prices],
        'low': [p - np.random.uniform(0, 2) for p in prices],
        'close': prices,
        'volume': [np.random.randint(100, 1000) for _ in range(100)]
    })
    
    return df

def test_strong_zones():
    """
    Test the strong zones functionality.
    """
    print("=== Testing Strong Zones Implementation ===")
    
    # Create test data
    print("1. Creating test data...")
    df = create_test_data()
    print(f"   Created DataFrame with {len(df)} rows")
    
    # Process the DataFrame
    print("2. Processing DataFrame...")
    df_processed = process_dataframe(df)
    
    if df_processed is None:
        print("   ✗ Processing failed - returned None")
        return False
    
    print(f"   ✓ Processing completed - {len(df_processed)} rows")
    
    # Check if strong_zone column exists
    print("3. Checking strong_zone column...")
    if 'strong_zone' not in df_processed.columns:
        print("   ✗ strong_zone column not found")
        return False
    
    print("   ✓ strong_zone column found")
    
    # Check column values
    strong_zones = df_processed[df_processed['strong_zone'] != '']
    print(f"   Found {len(strong_zones)} strong zones:")
    
    if len(strong_zones) > 0:
        zone_counts = strong_zones['strong_zone'].value_counts()
        for zone_type, count in zone_counts.items():
            print(f"     - {zone_type}: {count}")
    else:
        print("     - No strong zones detected (this is normal for random test data)")
    
    # Check all expected columns are present
    print("4. Checking all expected columns...")
    expected_columns = [
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'WMA5', 'WMA10', 'WMA45', 'WMA65', 'WMA90',
        'RSI_14', 'ATR_14', 'crossover_signal',
        'Zones_sup_dem', 'Zone_Status', 'strong_zone'
    ]
    
    missing_columns = [col for col in expected_columns if col not in df_processed.columns]
    if missing_columns:
        print(f"   ✗ Missing columns: {missing_columns}")
        return False
    
    print("   ✓ All expected columns present")
    
    # Save test output to verify CSV format
    print("5. Saving test output...")
    output_file = 'test_strong_zones_output.csv'
    df_processed.to_csv(output_file, index=False)
    print(f"   ✓ Test output saved to {output_file}")
    
    # Show sample of the data
    print("6. Sample data (first 5 rows with relevant columns):")
    sample_cols = ['timestamp', 'crossover_signal', 'Zones_sup_dem', 'strong_zone']
    print(df_processed[sample_cols].head().to_string(index=False))
    
    return True

if __name__ == "__main__":
    success = test_strong_zones()
    if success:
        print("\n✓ All tests passed! Strong zones implementation is working correctly.")
    else:
        print("\n✗ Tests failed! Please check the implementation.")
    
    sys.exit(0 if success else 1)
