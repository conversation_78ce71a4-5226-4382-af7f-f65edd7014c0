2025-07-06 00:20:24 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 00:20:24 INFO: === ****************START LOGGING* (00:20:24) ************ ===
2025-07-06 00:20:24 INFO: === ****************************************************** ===
2025-07-06 00:20:24 INFO: === Algo Trading Bot Started ===
2025-07-06 00:20:24 INFO: Output for processed NIFTY data: processed_files
2025-07-06 00:20:24 INFO: Output for processed Options data: processed_options_files
2025-07-06 00:20:24 INFO: Options input file: User_options_input.csv
2025-07-06 00:20:24 INFO: ============================================================
2025-07-06 00:20:24 INFO: --- Processing Historical NIFTY Data (00:20:24) ---
2025-07-06 00:20:24 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 00:20:24 INFO:  Fetching 13 60min data...
2025-07-06 00:20:24 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 00:20:24 INFO:  Fetched 314 new records
2025-07-06 00:20:24 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 00:20:24 INFO:  Fetching 13 dailymin data...
2025-07-06 00:20:24 INFO: Daily data ready to be fetch from API
2025-07-06 00:20:24 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 00:20:24 INFO:  Fetched 249 new records
2025-07-06 00:20:24 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 00:20:24 INFO:  Fetching 13 5min data...
2025-07-06 00:20:24 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 00:20:24 INFO:  Fetched 1125 new records
2025-07-06 00:20:25 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 00:20:25 INFO:  Fetching 13 1min data...
2025-07-06 00:20:25 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 00:20:25 INFO:  Fetched 3373 new records
2025-07-06 00:20:25 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 00:20:25 INFO: ============================================================
2025-07-06 00:20:25 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 00:20:25 INFO: ============================================================

2025-07-06 00:52:04 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 00:52:04 INFO: === ****************START LOGGING* (00:52:04) ************ ===
2025-07-06 00:52:04 INFO: === ****************************************************** ===
2025-07-06 00:52:04 INFO: === Algo Trading Bot Started ===
2025-07-06 00:52:04 INFO: Output for processed NIFTY data: processed_files
2025-07-06 00:52:04 INFO: Output for processed Options data: processed_options_files
2025-07-06 00:52:04 INFO: Options input file: User_options_input.csv
2025-07-06 00:52:04 INFO: ============================================================
2025-07-06 00:52:04 INFO: --- Processing Historical NIFTY Data (00:52:04) ---
2025-07-06 00:52:04 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 00:52:04 INFO:  Fetching 13 dailymin data...
2025-07-06 00:52:05 INFO: Daily data ready to be fetch from API
2025-07-06 00:52:05 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 00:52:05 INFO:  Fetched 249 new records
2025-07-06 00:52:05 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 00:52:05 INFO:  Fetching 13 5min data...
2025-07-06 00:52:05 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 00:52:05 INFO:  Fetched 1125 new records
2025-07-06 00:52:05 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 00:52:05 INFO:  Fetching 13 1min data...
2025-07-06 00:52:05 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 00:52:05 INFO:  Fetched 3373 new records
2025-07-06 00:52:06 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 00:52:06 INFO:  Fetching 13 60min data...
2025-07-06 00:52:06 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 00:52:06 INFO:  Fetched 314 new records
2025-07-06 00:52:06 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 00:52:06 INFO: ============================================================
2025-07-06 00:52:06 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 00:52:06 INFO: ============================================================

2025-07-06 01:02:06 INFO: --- Processing Historical NIFTY Data (01:02:06) ---
2025-07-06 01:02:06 INFO:  Fetching 13 5min data...
2025-07-06 01:02:06 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:02:06 INFO:  Fetched 1125 new records
2025-07-06 01:02:06 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 01:02:06 INFO:  Fetching 13 1min data...
2025-07-06 01:02:07 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:02:07 INFO:  Fetched 3373 new records
2025-07-06 01:02:07 ERROR:  Error saving processed data for interval 1: [Errno 13] Permission denied: 'processed_files\\NIFTY\\2025-07-06\\NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv'
2025-07-06 01:02:07 INFO: ============================================================
2025-07-06 01:02:07 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 01:02:07 INFO: ============================================================

2025-07-06 01:23:19 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 01:23:19 INFO: === ****************START LOGGING* (01:23:19) ************ ===
2025-07-06 01:23:19 INFO: === ****************************************************** ===
2025-07-06 01:23:19 INFO: === Algo Trading Bot Started ===
2025-07-06 01:23:19 INFO: Output for processed NIFTY data: processed_files
2025-07-06 01:23:19 INFO: Output for processed Options data: processed_options_files
2025-07-06 01:23:19 INFO: Options input file: User_options_input.csv
2025-07-06 01:23:19 INFO: ============================================================
2025-07-06 01:23:19 INFO: --- Processing Historical NIFTY Data (01:23:19) ---
2025-07-06 01:23:19 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 01:23:19 INFO:  Fetching 13 5min data...
2025-07-06 01:23:20 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:23:20 INFO:  Fetched 1125 new records
2025-07-06 01:23:20 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 01:23:20 INFO:  Fetching 13 dailymin data...
2025-07-06 01:23:20 INFO: Daily data ready to be fetch from API
2025-07-06 01:23:20 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 01:23:20 INFO:  Fetched 249 new records
2025-07-06 01:23:20 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 01:23:20 INFO:  Fetching 13 60min data...
2025-07-06 01:23:20 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:23:20 INFO:  Fetched 314 new records
2025-07-06 01:23:20 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 01:23:20 INFO:  Fetching 13 1min data...
2025-07-06 01:23:21 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:23:21 INFO:  Fetched 3373 new records
2025-07-06 01:23:21 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 01:23:21 INFO: ============================================================
2025-07-06 01:23:21 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 01:23:21 INFO: ============================================================

2025-07-06 01:41:08 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 01:41:08 INFO: === ****************START LOGGING* (01:41:08) ************ ===
2025-07-06 01:41:08 INFO: === ****************************************************** ===
2025-07-06 01:41:08 INFO: === Algo Trading Bot Started ===
2025-07-06 01:41:08 INFO: Output for processed NIFTY data: processed_files
2025-07-06 01:41:08 INFO: Output for processed Options data: processed_options_files
2025-07-06 01:41:08 INFO: Options input file: User_options_input.csv
2025-07-06 01:41:08 INFO: ============================================================
2025-07-06 01:41:08 INFO: --- Processing Historical NIFTY Data (01:41:08) ---
2025-07-06 01:41:08 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 01:41:08 INFO:  Fetching 13 60min data...
2025-07-06 01:41:08 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:41:08 INFO:  Fetched 314 new records
2025-07-06 01:41:08 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 01:41:08 INFO:  Fetching 13 1min data...
2025-07-06 01:41:09 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:41:09 INFO:  Fetched 3373 new records
2025-07-06 01:41:09 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 01:41:09 INFO:  Fetching 13 dailymin data...
2025-07-06 01:41:09 INFO: Daily data ready to be fetch from API
2025-07-06 01:41:09 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 01:41:09 INFO:  Fetched 249 new records
2025-07-06 01:41:09 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 01:41:09 INFO:  Fetching 13 5min data...
2025-07-06 01:41:09 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:41:09 INFO:  Fetched 1125 new records
2025-07-06 01:41:10 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 01:41:10 INFO: ============================================================
2025-07-06 01:41:10 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 01:41:10 INFO: ============================================================

2025-07-06 01:43:42 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 01:43:42 INFO: === ****************START LOGGING* (01:43:42) ************ ===
2025-07-06 01:43:42 INFO: === ****************************************************** ===
2025-07-06 01:43:42 INFO: === Algo Trading Bot Started ===
2025-07-06 01:43:42 INFO: Output for processed NIFTY data: processed_files
2025-07-06 01:43:42 INFO: Output for processed Options data: processed_options_files
2025-07-06 01:43:42 INFO: Options input file: User_options_input.csv
2025-07-06 01:43:42 INFO: ============================================================
2025-07-06 01:43:42 INFO: --- Processing Historical NIFTY Data (01:43:42) ---
2025-07-06 01:43:42 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 01:43:42 INFO:  Fetching 13 dailymin data...
2025-07-06 01:43:42 INFO: Daily data ready to be fetch from API
2025-07-06 01:43:42 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 01:43:42 INFO:  Fetched 249 new records
2025-07-06 01:43:42 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 01:43:42 INFO:  Fetching 13 60min data...
2025-07-06 01:43:42 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:43:42 INFO:  Fetched 314 new records
2025-07-06 01:43:42 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 01:43:42 INFO:  Fetching 13 1min data...
2025-07-06 01:43:42 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:43:42 INFO:  Fetched 3373 new records
2025-07-06 01:43:43 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 01:43:43 INFO:  Fetching 13 5min data...
2025-07-06 01:43:43 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:43:43 INFO:  Fetched 1125 new records
2025-07-06 01:43:43 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 01:43:43 INFO: ============================================================
2025-07-06 01:43:43 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 01:43:43 INFO: ============================================================

2025-07-06 01:55:14 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 01:55:14 INFO: === ****************START LOGGING* (01:55:14) ************ ===
2025-07-06 01:55:14 INFO: === ****************************************************** ===
2025-07-06 01:55:14 INFO: === Algo Trading Bot Started ===
2025-07-06 01:55:14 INFO: Output for processed NIFTY data: processed_files
2025-07-06 01:55:14 INFO: Output for processed Options data: processed_options_files
2025-07-06 01:55:14 INFO: Options input file: User_options_input.csv
2025-07-06 01:55:14 INFO: ============================================================
2025-07-06 01:55:14 INFO: --- Processing Historical NIFTY Data (01:55:14) ---
2025-07-06 01:55:14 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 01:55:14 INFO:  Fetching 13 5min data...
2025-07-06 01:55:14 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:55:14 INFO:  Fetched 1125 new records
2025-07-06 01:55:14 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 01:55:14 INFO:  Fetching 13 1min data...
2025-07-06 01:55:15 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:55:15 INFO:  Fetched 3373 new records
2025-07-06 01:55:15 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 01:55:15 INFO:  Fetching 13 60min data...
2025-07-06 01:55:15 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 01:55:15 INFO:  Fetched 314 new records
2025-07-06 01:55:15 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 01:55:15 INFO:  Fetching 13 dailymin data...
2025-07-06 01:55:16 INFO: Daily data ready to be fetch from API
2025-07-06 01:55:16 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 01:55:16 INFO:  Fetched 249 new records
2025-07-06 01:55:16 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 01:55:16 INFO: ============================================================
2025-07-06 01:55:16 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 01:55:16 INFO: ============================================================

2025-07-06 02:02:12 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:02:12 INFO: === ****************START LOGGING* (02:02:12) ************ ===
2025-07-06 02:02:12 INFO: === ****************************************************** ===
2025-07-06 02:02:12 INFO: === Algo Trading Bot Started ===
2025-07-06 02:02:12 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:02:12 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:02:12 INFO: Options input file: User_options_input.csv
2025-07-06 02:02:12 INFO: ============================================================
2025-07-06 02:02:12 INFO: --- Processing Historical NIFTY Data (02:02:12) ---
2025-07-06 02:02:12 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:02:12 INFO:  Fetching 13 1min data...
2025-07-06 02:02:13 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:02:13 INFO:  Fetched 3373 new records
2025-07-06 02:02:13 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:02:13 INFO:  Fetching 13 dailymin data...
2025-07-06 02:02:13 INFO: Daily data ready to be fetch from API
2025-07-06 02:02:13 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:02:13 INFO:  Fetched 249 new records
2025-07-06 02:02:13 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:02:13 INFO:  Fetching 13 5min data...
2025-07-06 02:02:13 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:02:13 INFO:  Fetched 1125 new records
2025-07-06 02:02:14 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:02:14 INFO:  Fetching 13 60min data...
2025-07-06 02:02:14 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:02:14 INFO:  Fetched 314 new records
2025-07-06 02:02:14 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:02:14 INFO: ============================================================
2025-07-06 02:02:14 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:02:14 INFO: ============================================================

2025-07-06 02:24:24 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:24:24 INFO: === ****************START LOGGING* (02:24:24) ************ ===
2025-07-06 02:24:24 INFO: === ****************************************************** ===
2025-07-06 02:24:24 INFO: === Algo Trading Bot Started ===
2025-07-06 02:24:24 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:24:24 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:24:24 INFO: Options input file: User_options_input.csv
2025-07-06 02:24:24 INFO: ============================================================
2025-07-06 02:24:24 INFO: --- Processing Historical NIFTY Data (02:24:24) ---
2025-07-06 02:24:24 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:24:24 INFO:  Fetching 13 5min data...
2025-07-06 02:24:24 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:24:24 INFO:  Fetched 1125 new records
2025-07-06 02:24:25 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:24:25 INFO:  Fetching 13 1min data...
2025-07-06 02:24:25 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:24:25 INFO:  Fetched 3373 new records
2025-07-06 02:24:25 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:24:25 INFO:  Fetching 13 60min data...
2025-07-06 02:24:26 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:24:26 INFO:  Fetched 314 new records
2025-07-06 02:24:26 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:24:26 INFO:  Fetching 13 dailymin data...
2025-07-06 02:24:26 INFO: Daily data ready to be fetch from API
2025-07-06 02:24:26 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:24:26 INFO:  Fetched 249 new records
2025-07-06 02:24:26 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:24:26 INFO: ============================================================
2025-07-06 02:24:26 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:24:26 INFO: ============================================================

2025-07-06 02:37:49 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:37:49 INFO: === ****************START LOGGING* (02:37:49) ************ ===
2025-07-06 02:37:49 INFO: === ****************************************************** ===
2025-07-06 02:37:49 INFO: === Algo Trading Bot Started ===
2025-07-06 02:37:49 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:37:49 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:37:49 INFO: Options input file: User_options_input.csv
2025-07-06 02:37:49 INFO: ============================================================
2025-07-06 02:37:49 INFO: --- Processing Historical NIFTY Data (02:37:49) ---
2025-07-06 02:37:49 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:37:49 INFO:  Fetching 13 60min data...
2025-07-06 02:37:49 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:37:49 INFO:  Fetched 314 new records
2025-07-06 02:37:49 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:37:49 INFO:  Fetching 13 dailymin data...
2025-07-06 02:37:50 INFO: Daily data ready to be fetch from API
2025-07-06 02:37:50 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:37:50 INFO:  Fetched 249 new records
2025-07-06 02:37:50 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:37:50 INFO:  Fetching 13 1min data...
2025-07-06 02:37:50 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:37:50 INFO:  Fetched 3373 new records
2025-07-06 02:37:51 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:37:51 INFO:  Fetching 13 5min data...
2025-07-06 02:37:51 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:37:51 INFO:  Fetched 1125 new records
2025-07-06 02:37:51 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:37:51 INFO: ============================================================
2025-07-06 02:37:51 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:37:51 INFO: ============================================================

2025-07-06 02:48:58 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:48:58 INFO: === ****************START LOGGING* (02:48:58) ************ ===
2025-07-06 02:48:58 INFO: === ****************************************************** ===
2025-07-06 02:48:58 INFO: === Algo Trading Bot Started ===
2025-07-06 02:48:58 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:48:58 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:48:58 INFO: Options input file: User_options_input.csv
2025-07-06 02:48:58 INFO: ============================================================
2025-07-06 02:48:58 INFO: --- Processing Historical NIFTY Data (02:48:58) ---
2025-07-06 02:48:58 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:48:58 INFO:  Fetching 13 5min data...
2025-07-06 02:48:59 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:48:59 INFO:  Fetched 1125 new records
2025-07-06 02:48:59 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:48:59 INFO:  Fetching 13 dailymin data...
2025-07-06 02:49:00 INFO: Daily data ready to be fetch from API
2025-07-06 02:49:00 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:49:00 INFO:  Fetched 249 new records
2025-07-06 02:49:00 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:49:00 INFO:  Fetching 13 60min data...
2025-07-06 02:49:00 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:49:00 INFO:  Fetched 314 new records
2025-07-06 02:49:00 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:49:00 INFO:  Fetching 13 1min data...
2025-07-06 02:49:00 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:49:00 INFO:  Fetched 3373 new records
2025-07-06 02:49:01 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:49:01 INFO: ============================================================
2025-07-06 02:49:01 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:49:01 INFO: ============================================================

2025-07-06 02:54:13 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:54:13 INFO: === ****************START LOGGING* (02:54:13) ************ ===
2025-07-06 02:54:13 INFO: === ****************************************************** ===
2025-07-06 02:54:13 INFO: === Algo Trading Bot Started ===
2025-07-06 02:54:13 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:54:13 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:54:13 INFO: Options input file: User_options_input.csv
2025-07-06 02:54:13 INFO: ============================================================
2025-07-06 02:54:13 INFO: --- Processing Historical NIFTY Data (02:54:13) ---
2025-07-06 02:54:13 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:54:13 INFO:  Fetching 13 dailymin data...
2025-07-06 02:54:13 INFO: Daily data ready to be fetch from API
2025-07-06 02:54:13 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:54:13 INFO:  Fetched 249 new records
2025-07-06 02:54:13 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:54:13 INFO:  Fetching 13 5min data...
2025-07-06 02:54:14 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:54:14 INFO:  Fetched 1125 new records
2025-07-06 02:54:14 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:54:14 INFO:  Fetching 13 60min data...
2025-07-06 02:54:14 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:54:14 INFO:  Fetched 314 new records
2025-07-06 02:54:14 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:54:14 INFO:  Fetching 13 1min data...
2025-07-06 02:54:14 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:54:14 INFO:  Fetched 3373 new records
2025-07-06 02:54:15 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:54:15 INFO: ============================================================
2025-07-06 02:54:15 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:54:15 INFO: ============================================================

2025-07-06 02:57:07 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:57:07 INFO: === ****************START LOGGING* (02:57:07) ************ ===
2025-07-06 02:57:07 INFO: === ****************************************************** ===
2025-07-06 02:57:07 INFO: === Algo Trading Bot Started ===
2025-07-06 02:57:07 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:57:07 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:57:07 INFO: Options input file: User_options_input.csv
2025-07-06 02:57:07 INFO: ============================================================
2025-07-06 02:57:07 INFO: --- Processing Historical NIFTY Data (02:57:07) ---
2025-07-06 02:57:07 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:57:07 INFO:  Fetching 13 1min data...
2025-07-06 02:57:07 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:57:07 INFO:  Fetched 3373 new records
2025-07-06 02:57:07 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:57:07 INFO:  Fetching 13 dailymin data...
2025-07-06 02:57:08 INFO: Daily data ready to be fetch from API
2025-07-06 02:57:08 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:57:08 INFO:  Fetched 249 new records
2025-07-06 02:57:08 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:57:08 INFO:  Fetching 13 60min data...
2025-07-06 02:57:08 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:57:08 INFO:  Fetched 314 new records
2025-07-06 02:57:08 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:57:08 INFO:  Fetching 13 5min data...
2025-07-06 02:57:08 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:57:08 INFO:  Fetched 1125 new records
2025-07-06 02:57:08 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:57:08 INFO: ============================================================
2025-07-06 02:57:08 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:57:08 INFO: ============================================================

2025-07-06 02:59:38 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-06 02:59:38 INFO: === ****************START LOGGING* (02:59:38) ************ ===
2025-07-06 02:59:38 INFO: === ****************************************************** ===
2025-07-06 02:59:38 INFO: === Algo Trading Bot Started ===
2025-07-06 02:59:38 INFO: Output for processed NIFTY data: processed_files
2025-07-06 02:59:38 INFO: Output for processed Options data: processed_options_files
2025-07-06 02:59:38 INFO: Options input file: User_options_input.csv
2025-07-06 02:59:38 INFO: ============================================================
2025-07-06 02:59:38 INFO: --- Processing Historical NIFTY Data (02:59:38) ---
2025-07-06 02:59:38 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-06 02:59:38 INFO:  Fetching 13 60min data...
2025-07-06 02:59:38 INFO: Data fetched from 2025-05-05 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:59:38 INFO:  Fetched 314 new records
2025-07-06 02:59:38 INFO:  Saved processed file: NIFTY_60min_2025-05-05_to_2025-07-04_processed.csv
2025-07-06 02:59:38 INFO:  Fetching 13 dailymin data...
2025-07-06 02:59:38 INFO: Daily data ready to be fetch from API
2025-07-06 02:59:38 INFO: Data fetched from 2024-07-04 to 2025-07-04
2025-07-06 02:59:38 INFO:  Fetched 249 new records
2025-07-06 02:59:38 INFO:  Saved processed file: NIFTY_daily_2024-07-04_to_2025-07-04_processed.csv
2025-07-06 02:59:38 INFO:  Fetching 13 1min data...
2025-07-06 02:59:39 INFO: Data fetched from 2025-06-24 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:59:39 INFO:  Fetched 3373 new records
2025-07-06 02:59:39 INFO:  Saved processed file: NIFTY_1min_2025-06-24_to_2025-07-04_processed.csv
2025-07-06 02:59:39 INFO:  Fetching 13 5min data...
2025-07-06 02:59:39 INFO: Data fetched from 2025-06-14 09:15:00 to 2025-07-04 15:29:00
2025-07-06 02:59:39 INFO:  Fetched 1125 new records
2025-07-06 02:59:39 INFO:  Saved processed file: NIFTY_5min_2025-06-14_to_2025-07-04_processed.csv
2025-07-06 02:59:39 INFO: ============================================================
2025-07-06 02:59:39 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-07-06 02:59:39 INFO: ============================================================

