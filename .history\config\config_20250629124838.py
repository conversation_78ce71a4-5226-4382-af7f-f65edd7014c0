# config.py
import datetime

# === Credentials & API Keys ===
# It's recommended to load these from environment variables or a secure vault
# TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUzNTUyOTAwLCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.MUxSA-n_396kn6Cp_zVDbhTkGY85USrRF-RjuYdNSFz5RLqDn1qyoiJ4663aWQJEPb6pPhyGozznSXDeSK_y8w"
TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUzNTkwMjQ0LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.NrF4-on2lhpAr1No7od5q6x6BIuS2VvX3nfsHXDS2_pSlpqMHQ9Xy9y1lW1qX2zyqUFJRQrSr_sDcJLy8NI3pw"
CLIENT_CODE = "1105577608"

# === Directory and File Paths ===
LOG_DIR = "logs"
OUTPUT_DIR = "processed_files"
OPTIONS_OUTPUT_DIR = "processed_options_files"
OUTPUT_DIR_TRADE_LOG = "trade_log_files"
DEPENDENCIES_DIR = "Dependencies"

# === Input Files ===
OPTIONS_CSV_FILE = "User_options_input.csv"
OPTIONS_LOT_SIZE_CSV = "Nse_Fno_Lot_Size.csv"

# === Trading Parameters ===
OPTION_TIMEFRAMES = ["1", "5"]  # 1min/10days, 5min/20days, 60min/60days
FETCH_INTERVAL_SECONDS = 20  # Interval for the main loop

# === Market Timings ===
MARKET_START = datetime.time(9, 15)
MARKET_CLOSE = datetime.time(15, 29)
DAY_END = datetime.time(15, 35)  # Used for end-of-day processing
# ===
MARKET_START_DAY = datetime.datetime.combine(datetime.date.today(), MARKET_START)
MARKET_CLOSE_DAY = datetime.datetime.combine(datetime.date.today(), MARKET_CLOSE)


# === Instrument Configuration ===
NIFTY_SECURITY_ID = "13"
NIFTY_EXCHANGE_SEGMENT = "IDX_I"
NIFTY_INSTRUMENT_TYPE = "INDEX"