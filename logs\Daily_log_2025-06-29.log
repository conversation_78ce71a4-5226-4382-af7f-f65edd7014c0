2025-06-29 12:21:29 INFO: === ****************************************************** ===
2025-06-29 12:21:29 INFO: === ****************START LOGGING* (12:21:29) ************ ===
2025-06-29 12:21:29 INFO: === ****************************************************** ===
2025-06-29 12:21:29 INFO: === Algo Trading Bot Started ===
2025-06-29 12:21:29 INFO: Output for processed NIFTY data: processed_files
2025-06-29 12:21:29 INFO: Output for processed Options data: processed_options_files
2025-06-29 12:21:29 INFO: Options input file: User_options_input.csv
2025-06-29 12:21:29 INFO: ============================================================
2025-06-29 12:21:29 INFO: --- Processing Historical NIFTY Data (12:21:29) ---
2025-06-29 12:21:29 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 12:21:29 INFO:  Fetching NIFTY dailymin data...
2025-06-29 12:21:29 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 12:21:29 INFO:  Fetched 248 new records
2025-06-29 12:21:29 INFO:  Saved processed file: NIFTY_INDEX_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 12:21:29 INFO:  Fetching NIFTY 60min data...
2025-06-29 12:21:30 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:21:30 INFO:  Fetched 293 new records
2025-06-29 12:21:30 INFO:  Saved processed file: NIFTY_INDEX_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 12:21:30 INFO:  Fetching NIFTY 5min data...
2025-06-29 12:21:30 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:21:30 INFO:  Fetched 1124 new records
2025-06-29 12:21:30 INFO:  Saved processed file: NIFTY_INDEX_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 12:21:30 INFO:  Fetching NIFTY 1min data...
2025-06-29 12:21:30 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:21:30 INFO:  Fetched 2624 new records
2025-06-29 12:21:31 INFO:  Saved processed file: NIFTY_INDEX_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 12:21:31 INFO: State: SCANNING for trade signals.
2025-06-29 12:21:31 INFO: Entering function: look_for_trade_signals
2025-06-29 12:21:31 INFO: 1-min latest crossover: downward
2025-06-29 12:21:31 INFO: 5-min latest crossover: upward
2025-06-29 12:21:31 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 12:21:31 INFO: ============================================================
2025-06-29 12:21:31 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 12:21:31 INFO: ============================================================

2025-06-29 12:37:02 INFO: === ****************************************************** ===
2025-06-29 12:37:02 INFO: === ****************START LOGGING* (12:37:02) ************ ===
2025-06-29 12:37:02 INFO: === ****************************************************** ===
2025-06-29 12:37:02 INFO: === Algo Trading Bot Started ===
2025-06-29 12:37:02 INFO: Output for processed NIFTY data: processed_files
2025-06-29 12:37:02 INFO: Output for processed Options data: processed_options_files
2025-06-29 12:37:02 INFO: Options input file: User_options_input.csv
2025-06-29 12:37:02 INFO: ============================================================
2025-06-29 12:37:02 INFO: --- Processing Historical NIFTY Data (12:37:02) ---
2025-06-29 12:37:02 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 12:37:02 INFO:  Fetching NIFTY dailymin data...
2025-06-29 12:37:02 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 12:37:02 INFO:  Fetched 248 new records
2025-06-29 12:37:03 INFO:  Saved processed file: NIFTY_INDEX_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 12:37:03 INFO:  Fetching NIFTY 5min data...
2025-06-29 12:37:03 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:37:03 INFO:  Fetched 1124 new records
2025-06-29 12:37:03 INFO:  Saved processed file: NIFTY_INDEX_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 12:37:03 INFO:  Fetching NIFTY 60min data...
2025-06-29 12:37:03 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:37:03 INFO:  Fetched 293 new records
2025-06-29 12:37:03 INFO:  Saved processed file: NIFTY_INDEX_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 12:37:03 INFO:  Fetching NIFTY 1min data...
2025-06-29 12:37:03 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:37:03 INFO:  Fetched 2624 new records
2025-06-29 12:37:04 INFO:  Saved processed file: NIFTY_INDEX_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 12:37:04 INFO: State: SCANNING for trade signals.
2025-06-29 12:37:04 INFO: Entering function: look_for_trade_signals
2025-06-29 12:37:04 INFO: 1-min latest crossover: downward
2025-06-29 12:37:04 INFO: 5-min latest crossover: upward
2025-06-29 12:37:04 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 12:37:04 INFO: ============================================================
2025-06-29 12:37:04 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 12:37:04 INFO: ============================================================

2025-06-29 12:37:24 INFO: --- Processing Historical NIFTY Data (12:37:24) ---
2025-06-29 12:37:24 INFO:  Fetching NIFTY 5min data...
2025-06-29 12:37:24 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:37:24 INFO:  Fetched 1124 new records
2025-06-29 12:37:24 INFO:  Saved processed file: NIFTY_INDEX_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 12:37:24 INFO:  Fetching NIFTY 1min data...
2025-06-29 12:37:25 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:37:25 INFO:  Fetched 2624 new records
2025-06-29 12:37:25 INFO:  Saved processed file: NIFTY_INDEX_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 12:37:25 INFO: State: SCANNING for trade signals.
2025-06-29 12:37:25 INFO: Entering function: look_for_trade_signals
2025-06-29 12:37:25 INFO: 1-min latest crossover: downward
2025-06-29 12:37:25 INFO: 5-min latest crossover: upward
2025-06-29 12:37:25 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 12:37:25 INFO: ============================================================
2025-06-29 12:37:25 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 12:37:25 INFO: ============================================================

2025-06-29 12:56:43 INFO: === ****************************************************** ===
2025-06-29 12:56:43 INFO: === ****************START LOGGING* (12:56:43) ************ ===
2025-06-29 12:56:43 INFO: === ****************************************************** ===
2025-06-29 12:56:43 INFO: === Algo Trading Bot Started ===
2025-06-29 12:56:43 INFO: Output for processed NIFTY data: processed_files
2025-06-29 12:56:43 INFO: Output for processed Options data: processed_options_files
2025-06-29 12:56:43 INFO: Options input file: User_options_input.csv
2025-06-29 12:56:43 INFO: ============================================================
2025-06-29 12:56:43 INFO: --- Processing Historical NIFTY Data (12:56:43) ---
2025-06-29 12:56:43 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 12:56:43 INFO:  Fetching NIFTY 1min data...
2025-06-29 12:57:40 INFO: === ****************************************************** ===
2025-06-29 12:57:40 INFO: === ****************START LOGGING* (12:57:40) ************ ===
2025-06-29 12:57:40 INFO: === ****************************************************** ===
2025-06-29 12:57:40 INFO: === Algo Trading Bot Started ===
2025-06-29 12:57:40 INFO: Output for processed NIFTY data: processed_files
2025-06-29 12:57:40 INFO: Output for processed Options data: processed_options_files
2025-06-29 12:57:40 INFO: Options input file: User_options_input.csv
2025-06-29 12:57:40 INFO: ============================================================
2025-06-29 12:57:40 INFO: --- Processing Historical NIFTY Data (12:57:40) ---
2025-06-29 12:57:40 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 12:57:40 INFO:  Fetching NIFTY dailymin data...
2025-06-29 12:58:24 INFO: === ****************************************************** ===
2025-06-29 12:58:24 INFO: === ****************START LOGGING* (12:58:24) ************ ===
2025-06-29 12:58:24 INFO: === ****************************************************** ===
2025-06-29 12:58:24 INFO: === Algo Trading Bot Started ===
2025-06-29 12:58:24 INFO: Output for processed NIFTY data: processed_files
2025-06-29 12:58:24 INFO: Output for processed Options data: processed_options_files
2025-06-29 12:58:24 INFO: Options input file: User_options_input.csv
2025-06-29 12:58:24 INFO: ============================================================
2025-06-29 12:58:24 INFO: --- Processing Historical NIFTY Data (12:58:24) ---
2025-06-29 12:58:24 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 12:58:24 INFO:  Fetching NIFTY 5min data...
2025-06-29 12:58:24 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 12:58:24 INFO:  Fetched 1124 new records
2025-06-29 12:58:25 INFO:  Saved processed file: NIFTY_INDEX_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 12:58:25 INFO:  Fetching NIFTY dailymin data...
2025-06-29 13:00:28 INFO: === ****************************************************** ===
2025-06-29 13:00:28 INFO: === ****************START LOGGING* (13:00:28) ************ ===
2025-06-29 13:00:28 INFO: === ****************************************************** ===
2025-06-29 13:00:28 INFO: === Algo Trading Bot Started ===
2025-06-29 13:00:28 INFO: Output for processed NIFTY data: processed_files
2025-06-29 13:00:28 INFO: Output for processed Options data: processed_options_files
2025-06-29 13:00:28 INFO: Options input file: User_options_input.csv
2025-06-29 13:00:28 INFO: ============================================================
2025-06-29 13:00:28 INFO: --- Processing Historical NIFTY Data (13:00:28) ---
2025-06-29 13:00:28 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 13:00:28 INFO:  Quarter-hour mark (13:00): Adding 60min fetch
2025-06-29 13:00:28 INFO:  Fetching NIFTY 60min data...
2025-06-29 13:02:43 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 13:02:43 INFO:  Fetched 293 new records
2025-06-29 13:02:43 INFO:  Saved processed file: NIFTY_INDEX_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 13:02:43 INFO:  Fetching NIFTY 1min data...
2025-06-29 19:36:43 INFO: === ****************************************************** ===
2025-06-29 19:36:43 INFO: === ****************START LOGGING* (19:36:43) ************ ===
2025-06-29 19:36:43 INFO: === ****************************************************** ===
2025-06-29 19:36:43 INFO: === Algo Trading Bot Started ===
2025-06-29 19:36:43 INFO: Output for processed NIFTY data: processed_files
2025-06-29 19:36:43 INFO: Output for processed Options data: processed_options_files
2025-06-29 19:36:43 INFO: Options input file: User_options_input.csv
2025-06-29 19:36:43 INFO: ============================================================
2025-06-29 19:36:43 INFO: --- Processing Historical NIFTY Data (19:36:43) ---
2025-06-29 19:36:43 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 19:36:43 INFO:  Fetching 13 dailymin data...
2025-06-29 19:36:43 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:36:43 INFO:  Fetched 248 new records
2025-06-29 19:36:44 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:36:44 INFO:  Fetching 13 60min data...
2025-06-29 19:36:44 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:44 INFO:  Fetched 293 new records
2025-06-29 19:36:44 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:36:44 INFO:  Fetching 13 5min data...
2025-06-29 19:36:44 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:44 INFO:  Fetched 1124 new records
2025-06-29 19:36:44 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:36:44 INFO:  Fetching 13 1min data...
2025-06-29 19:36:45 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:45 INFO:  Fetched 2624 new records
2025-06-29 19:36:45 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:36:45 INFO:  Fetching 25 dailymin data...
2025-06-29 19:36:45 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:36:45 INFO:  Fetched 248 new records
2025-06-29 19:36:45 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:36:45 INFO:  Fetching 25 60min data...
2025-06-29 19:36:45 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:45 INFO:  Fetched 293 new records
2025-06-29 19:36:45 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:36:45 INFO:  Fetching 25 5min data...
2025-06-29 19:36:46 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:46 INFO:  Fetched 1124 new records
2025-06-29 19:36:46 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:36:46 INFO:  Fetching 25 1min data...
2025-06-29 19:36:46 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:46 INFO:  Fetched 2624 new records
2025-06-29 19:36:47 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:36:47 INFO:  Fetching 27 dailymin data...
2025-06-29 19:36:47 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:36:47 INFO:  Fetched 248 new records
2025-06-29 19:36:47 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:36:47 INFO:  Fetching 27 60min data...
2025-06-29 19:36:47 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:47 INFO:  Fetched 293 new records
2025-06-29 19:36:47 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:36:47 INFO:  Fetching 27 5min data...
2025-06-29 19:36:47 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:47 INFO:  Fetched 1124 new records
2025-06-29 19:36:48 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:36:48 INFO:  Fetching 27 1min data...
2025-06-29 19:36:48 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:36:48 INFO:  Fetched 2624 new records
2025-06-29 19:36:48 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:36:48 INFO: State: SCANNING for trade signals.
2025-06-29 19:36:48 INFO: Entering function: look_for_trade_signals
2025-06-29 19:36:48 INFO: 1-min latest crossover: downward
2025-06-29 19:36:48 INFO: 5-min latest crossover: downward
2025-06-29 19:36:48 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:36:48 INFO: ============================================================
2025-06-29 19:36:48 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:36:48 INFO: ============================================================

2025-06-29 19:37:08 INFO: --- Processing Historical NIFTY Data (19:37:08) ---
2025-06-29 19:37:08 INFO:  Fetching 13 5min data...
2025-06-29 19:37:09 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:37:09 INFO:  Fetched 1124 new records
2025-06-29 19:37:09 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:37:09 INFO:  Fetching 13 1min data...
2025-06-29 19:37:09 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:37:09 INFO:  Fetched 2624 new records
2025-06-29 19:37:10 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:37:10 INFO:  Fetching 25 5min data...
2025-06-29 19:37:10 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:37:10 INFO:  Fetched 1124 new records
2025-06-29 19:37:10 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:37:10 INFO:  Fetching 25 1min data...
2025-06-29 19:37:10 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:37:10 INFO:  Fetched 2624 new records
2025-06-29 19:37:11 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:37:11 INFO:  Fetching 27 5min data...
2025-06-29 19:37:11 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:37:11 INFO:  Fetched 1124 new records
2025-06-29 19:37:11 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:37:11 INFO:  Fetching 27 1min data...
2025-06-29 19:37:12 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:37:12 INFO:  Fetched 2624 new records
2025-06-29 19:47:03 INFO: === ****************************************************** ===
2025-06-29 19:47:03 INFO: === ****************START LOGGING* (19:47:03) ************ ===
2025-06-29 19:47:03 INFO: === ****************************************************** ===
2025-06-29 19:47:03 INFO: === Algo Trading Bot Started ===
2025-06-29 19:47:03 INFO: Output for processed NIFTY data: processed_files
2025-06-29 19:47:03 INFO: Output for processed Options data: processed_options_files
2025-06-29 19:47:03 INFO: Options input file: User_options_input.csv
2025-06-29 19:47:03 INFO: ============================================================
2025-06-29 19:47:03 INFO: --- Processing Historical NIFTY Data (19:47:03) ---
2025-06-29 19:47:03 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 19:47:03 INFO:  Fetching 13 5min data...
2025-06-29 19:47:03 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:03 INFO:  Fetched 1124 new records
2025-06-29 19:47:03 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:47:03 INFO:  Fetching 13 dailymin data...
2025-06-29 19:47:04 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:47:04 INFO:  Fetched 248 new records
2025-06-29 19:47:04 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:47:04 INFO:  Fetching 13 60min data...
2025-06-29 19:47:04 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:04 INFO:  Fetched 293 new records
2025-06-29 19:47:04 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:47:04 INFO:  Fetching 13 1min data...
2025-06-29 19:47:04 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:04 INFO:  Fetched 2624 new records
2025-06-29 19:47:05 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:47:05 INFO:  Fetching 25 5min data...
2025-06-29 19:47:05 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:05 INFO:  Fetched 1124 new records
2025-06-29 19:47:05 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:47:05 INFO:  Fetching 25 dailymin data...
2025-06-29 19:47:05 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:47:05 INFO:  Fetched 248 new records
2025-06-29 19:47:05 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:47:05 INFO:  Fetching 25 60min data...
2025-06-29 19:47:05 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:05 INFO:  Fetched 293 new records
2025-06-29 19:47:05 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:47:05 INFO:  Fetching 25 1min data...
2025-06-29 19:47:06 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:06 INFO:  Fetched 2624 new records
2025-06-29 19:47:06 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:47:06 INFO:  Fetching 27 5min data...
2025-06-29 19:47:06 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:06 INFO:  Fetched 1124 new records
2025-06-29 19:47:07 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:47:07 INFO:  Fetching 27 dailymin data...
2025-06-29 19:47:07 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:47:07 INFO:  Fetched 248 new records
2025-06-29 19:47:07 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:47:07 INFO:  Fetching 27 60min data...
2025-06-29 19:47:07 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:07 INFO:  Fetched 293 new records
2025-06-29 19:47:07 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:47:07 INFO:  Fetching 27 1min data...
2025-06-29 19:47:07 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:07 INFO:  Fetched 2624 new records
2025-06-29 19:47:08 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:47:08 INFO: State: SCANNING for trade signals.
2025-06-29 19:47:08 INFO: Entering function: look_for_trade_signals
2025-06-29 19:47:08 INFO: 1-min latest crossover: downward
2025-06-29 19:47:08 INFO: 5-min latest crossover: downward
2025-06-29 19:47:08 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:47:08 INFO: ============================================================
2025-06-29 19:47:08 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:47:08 INFO: ============================================================

2025-06-29 19:47:28 INFO: --- Processing Historical NIFTY Data (19:47:28) ---
2025-06-29 19:47:28 INFO:  Fetching 13 5min data...
2025-06-29 19:47:28 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:28 INFO:  Fetched 1124 new records
2025-06-29 19:47:28 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:47:28 INFO:  Fetching 13 1min data...
2025-06-29 19:47:29 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:29 INFO:  Fetched 2624 new records
2025-06-29 19:47:29 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:47:29 INFO:  Fetching 25 5min data...
2025-06-29 19:47:29 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:29 INFO:  Fetched 1124 new records
2025-06-29 19:47:29 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:47:29 INFO:  Fetching 25 1min data...
2025-06-29 19:47:30 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:30 INFO:  Fetched 2624 new records
2025-06-29 19:47:30 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:47:30 INFO:  Fetching 27 5min data...
2025-06-29 19:47:30 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:30 INFO:  Fetched 1124 new records
2025-06-29 19:47:31 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:47:31 INFO:  Fetching 27 1min data...
2025-06-29 19:47:31 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:47:31 INFO:  Fetched 2624 new records
2025-06-29 19:47:31 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:47:31 INFO: State: SCANNING for trade signals.
2025-06-29 19:47:31 INFO: Entering function: look_for_trade_signals
2025-06-29 19:47:31 INFO: 1-min latest crossover: downward
2025-06-29 19:47:31 INFO: 5-min latest crossover: downward
2025-06-29 19:47:31 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:47:31 INFO: ============================================================
2025-06-29 19:47:31 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:47:31 INFO: ============================================================

2025-06-29 19:49:44 INFO: === ****************************************************** ===
2025-06-29 19:49:44 INFO: === ****************START LOGGING* (19:49:44) ************ ===
2025-06-29 19:49:44 INFO: === ****************************************************** ===
2025-06-29 19:49:44 INFO: === Algo Trading Bot Started ===
2025-06-29 19:49:44 INFO: Output for processed NIFTY data: processed_files
2025-06-29 19:49:44 INFO: Output for processed Options data: processed_options_files
2025-06-29 19:49:44 INFO: Options input file: User_options_input.csv
2025-06-29 19:49:44 INFO: ============================================================
2025-06-29 19:49:44 INFO: --- Processing Historical NIFTY Data (19:49:44) ---
2025-06-29 19:49:44 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 19:49:44 INFO:  Fetching 13 5min data...
2025-06-29 19:49:44 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:44 INFO:  Fetched 1124 new records
2025-06-29 19:49:44 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:49:44 INFO:  Fetching 13 60min data...
2025-06-29 19:49:45 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:45 INFO:  Fetched 293 new records
2025-06-29 19:49:45 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:49:45 INFO:  Fetching 13 1min data...
2025-06-29 19:49:45 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:45 INFO:  Fetched 2624 new records
2025-06-29 19:49:45 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:49:45 INFO:  Fetching 13 dailymin data...
2025-06-29 19:49:46 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:49:46 INFO:  Fetched 248 new records
2025-06-29 19:49:46 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:49:46 INFO:  Fetching 25 5min data...
2025-06-29 19:49:46 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:46 INFO:  Fetched 1124 new records
2025-06-29 19:49:46 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:49:46 INFO:  Fetching 25 60min data...
2025-06-29 19:49:46 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:46 INFO:  Fetched 293 new records
2025-06-29 19:49:46 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:49:46 INFO:  Fetching 25 1min data...
2025-06-29 19:49:46 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:46 INFO:  Fetched 2624 new records
2025-06-29 19:49:47 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:49:47 INFO:  Fetching 25 dailymin data...
2025-06-29 19:49:47 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:49:47 INFO:  Fetched 248 new records
2025-06-29 19:49:47 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:49:47 INFO:  Fetching 27 5min data...
2025-06-29 19:49:47 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:47 INFO:  Fetched 1124 new records
2025-06-29 19:49:48 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:49:48 INFO:  Fetching 27 60min data...
2025-06-29 19:49:48 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:48 INFO:  Fetched 293 new records
2025-06-29 19:49:48 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:49:48 INFO:  Fetching 27 1min data...
2025-06-29 19:49:48 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:49:48 INFO:  Fetched 2624 new records
2025-06-29 19:49:49 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:49:49 INFO:  Fetching 27 dailymin data...
2025-06-29 19:49:49 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:49:49 INFO:  Fetched 248 new records
2025-06-29 19:49:49 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:49:49 INFO: State: SCANNING for trade signals.
2025-06-29 19:49:49 INFO: Entering function: look_for_trade_signals
2025-06-29 19:49:49 INFO: 1-min latest crossover: downward
2025-06-29 19:49:49 INFO: 5-min latest crossover: downward
2025-06-29 19:49:49 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:49:49 INFO: ============================================================
2025-06-29 19:49:49 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:49:49 INFO: ============================================================

2025-06-29 19:50:09 INFO: --- Processing Historical NIFTY Data (19:50:09) ---
2025-06-29 19:50:09 INFO:  Fetching 13 5min data...
2025-06-29 19:50:09 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:50:09 INFO:  Fetched 1124 new records
2025-06-29 19:50:09 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:50:09 INFO:  Fetching 13 1min data...
2025-06-29 19:50:10 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:50:10 INFO:  Fetched 2624 new records
2025-06-29 19:50:10 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:50:10 INFO:  Fetching 25 5min data...
2025-06-29 19:50:10 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:50:10 INFO:  Fetched 1124 new records
2025-06-29 19:50:10 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:50:10 INFO:  Fetching 25 1min data...
2025-06-29 19:50:11 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:50:11 INFO:  Fetched 2624 new records
2025-06-29 19:50:11 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:50:11 INFO:  Fetching 27 5min data...
2025-06-29 19:50:11 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:50:11 INFO:  Fetched 1124 new records
2025-06-29 19:50:11 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:50:11 INFO:  Fetching 27 1min data...
2025-06-29 19:50:12 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:50:12 INFO:  Fetched 2624 new records
2025-06-29 19:50:12 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:50:12 INFO: State: SCANNING for trade signals.
2025-06-29 19:50:12 INFO: Entering function: look_for_trade_signals
2025-06-29 19:50:12 INFO: 1-min latest crossover: downward
2025-06-29 19:50:12 INFO: 5-min latest crossover: downward
2025-06-29 19:50:12 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:50:12 INFO: ============================================================
2025-06-29 19:50:12 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:50:12 INFO: ============================================================

2025-06-29 19:53:29 INFO: === ****************************************************** ===
2025-06-29 19:53:29 INFO: === ****************START LOGGING* (19:53:29) ************ ===
2025-06-29 19:53:29 INFO: === ****************************************************** ===
2025-06-29 19:53:29 INFO: === Algo Trading Bot Started ===
2025-06-29 19:53:29 INFO: Output for processed NIFTY data: processed_files
2025-06-29 19:53:29 INFO: Output for processed Options data: processed_options_files
2025-06-29 19:53:29 INFO: Options input file: User_options_input.csv
2025-06-29 19:53:29 INFO: ============================================================
2025-06-29 19:53:29 INFO: --- Processing Historical NIFTY Data (19:53:29) ---
2025-06-29 19:53:29 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 19:53:29 INFO:  Fetching 13 dailymin data...
2025-06-29 19:53:29 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:53:29 INFO:  Fetched 248 new records
2025-06-29 19:53:29 ERROR:  Error saving processed data for interval daily: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:29 INFO:  Fetching 13 5min data...
2025-06-29 19:53:30 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:30 INFO:  Fetched 1124 new records
2025-06-29 19:53:30 ERROR:  Error saving processed data for interval 5: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:30 INFO:  Fetching 13 1min data...
2025-06-29 19:53:30 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:30 INFO:  Fetched 2624 new records
2025-06-29 19:53:30 ERROR:  Error saving processed data for interval 1: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:30 INFO:  Fetching 13 60min data...
2025-06-29 19:53:31 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:31 INFO:  Fetched 293 new records
2025-06-29 19:53:31 ERROR:  Error saving processed data for interval 60: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:31 INFO:  Fetching 25 dailymin data...
2025-06-29 19:53:31 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:53:31 INFO:  Fetched 248 new records
2025-06-29 19:53:31 ERROR:  Error saving processed data for interval daily: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:31 INFO:  Fetching 25 5min data...
2025-06-29 19:53:31 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:31 INFO:  Fetched 1124 new records
2025-06-29 19:53:31 ERROR:  Error saving processed data for interval 5: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:31 INFO:  Fetching 25 1min data...
2025-06-29 19:53:31 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:31 INFO:  Fetched 2624 new records
2025-06-29 19:53:32 ERROR:  Error saving processed data for interval 1: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:32 INFO:  Fetching 25 60min data...
2025-06-29 19:53:32 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:32 INFO:  Fetched 293 new records
2025-06-29 19:53:32 ERROR:  Error saving processed data for interval 60: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:32 INFO:  Fetching 27 dailymin data...
2025-06-29 19:53:32 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:53:32 INFO:  Fetched 248 new records
2025-06-29 19:53:32 ERROR:  Error saving processed data for interval daily: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:32 INFO:  Fetching 27 5min data...
2025-06-29 19:53:32 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:32 INFO:  Fetched 1124 new records
2025-06-29 19:53:33 ERROR:  Error saving processed data for interval 5: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:33 INFO:  Fetching 27 1min data...
2025-06-29 19:53:33 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:33 INFO:  Fetched 2624 new records
2025-06-29 19:53:33 ERROR:  Error saving processed data for interval 1: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:33 INFO:  Fetching 27 60min data...
2025-06-29 19:53:34 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:53:34 INFO:  Fetched 293 new records
2025-06-29 19:53:34 ERROR:  Error saving processed data for interval 60: strptime() takes exactly 2 arguments (1 given)
2025-06-29 19:53:34 INFO: State: SCANNING for trade signals.
2025-06-29 19:53:34 INFO: Entering function: look_for_trade_signals
2025-06-29 19:53:34 INFO: 1-min latest crossover: downward
2025-06-29 19:53:34 INFO: 5-min latest crossover: downward
2025-06-29 19:53:34 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:53:34 INFO: ============================================================
2025-06-29 19:53:34 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:53:34 INFO: ============================================================

2025-06-29 19:54:03 INFO: === ****************************************************** ===
2025-06-29 19:54:03 INFO: === ****************START LOGGING* (19:54:03) ************ ===
2025-06-29 19:54:03 INFO: === ****************************************************** ===
2025-06-29 19:54:03 INFO: === Algo Trading Bot Started ===
2025-06-29 19:54:03 INFO: Output for processed NIFTY data: processed_files
2025-06-29 19:54:03 INFO: Output for processed Options data: processed_options_files
2025-06-29 19:54:03 INFO: Options input file: User_options_input.csv
2025-06-29 19:54:03 INFO: ============================================================
2025-06-29 19:54:03 INFO: --- Processing Historical NIFTY Data (19:54:03) ---
2025-06-29 19:54:03 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 19:54:03 INFO:  Fetching 13 60min data...
2025-06-29 19:54:03 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:03 INFO:  Fetched 293 new records
2025-06-29 19:54:03 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:54:03 INFO:  Fetching 13 5min data...
2025-06-29 19:54:03 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:03 INFO:  Fetched 1124 new records
2025-06-29 19:54:04 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:54:04 INFO:  Fetching 13 1min data...
2025-06-29 19:54:04 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:04 INFO:  Fetched 2624 new records
2025-06-29 19:54:04 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:54:04 INFO:  Fetching 13 dailymin data...
2025-06-29 19:54:05 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:54:05 INFO:  Fetched 248 new records
2025-06-29 19:54:05 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:54:05 INFO:  Fetching 25 60min data...
2025-06-29 19:54:05 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:05 INFO:  Fetched 293 new records
2025-06-29 19:54:05 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:54:05 INFO:  Fetching 25 5min data...
2025-06-29 19:54:05 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:05 INFO:  Fetched 1124 new records
2025-06-29 19:54:05 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:54:05 INFO:  Fetching 25 1min data...
2025-06-29 19:54:05 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:05 INFO:  Fetched 2624 new records
2025-06-29 19:54:06 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:54:06 INFO:  Fetching 25 dailymin data...
2025-06-29 19:54:06 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:54:06 INFO:  Fetched 248 new records
2025-06-29 19:54:06 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:54:06 INFO:  Fetching 27 60min data...
2025-06-29 19:54:06 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:06 INFO:  Fetched 293 new records
2025-06-29 19:54:06 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 19:54:06 INFO:  Fetching 27 5min data...
2025-06-29 19:54:06 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:06 INFO:  Fetched 1124 new records
2025-06-29 19:54:07 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:54:07 INFO:  Fetching 27 1min data...
2025-06-29 19:54:07 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:07 INFO:  Fetched 2624 new records
2025-06-29 19:54:07 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:54:07 INFO:  Fetching 27 dailymin data...
2025-06-29 19:54:08 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 19:54:08 INFO:  Fetched 248 new records
2025-06-29 19:54:08 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 19:54:08 INFO: State: SCANNING for trade signals.
2025-06-29 19:54:08 INFO: Entering function: look_for_trade_signals
2025-06-29 19:54:08 INFO: 1-min latest crossover: downward
2025-06-29 19:54:08 INFO: 5-min latest crossover: downward
2025-06-29 19:54:08 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 19:54:08 INFO: ============================================================
2025-06-29 19:54:08 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 19:54:08 INFO: ============================================================

2025-06-29 19:54:28 INFO: --- Processing Historical NIFTY Data (19:54:28) ---
2025-06-29 19:54:28 INFO:  Fetching 13 5min data...
2025-06-29 19:54:28 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:28 INFO:  Fetched 1124 new records
2025-06-29 19:54:28 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:54:28 INFO:  Fetching 13 1min data...
2025-06-29 19:54:28 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:28 INFO:  Fetched 2624 new records
2025-06-29 19:54:29 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:54:29 INFO:  Fetching 25 5min data...
2025-06-29 19:54:29 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:29 INFO:  Fetched 1124 new records
2025-06-29 19:54:29 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:54:29 INFO:  Fetching 25 1min data...
2025-06-29 19:54:29 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:29 INFO:  Fetched 2624 new records
2025-06-29 19:54:30 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 19:54:30 INFO:  Fetching 27 5min data...
2025-06-29 19:54:30 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:30 INFO:  Fetched 1124 new records
2025-06-29 19:54:30 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 19:54:30 INFO:  Fetching 27 1min data...
2025-06-29 19:54:30 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 19:54:30 INFO:  Fetched 2624 new records
2025-06-29 20:02:48 INFO: === ****************************************************** ===
2025-06-29 20:02:48 INFO: === ****************START LOGGING* (20:02:48) ************ ===
2025-06-29 20:02:48 INFO: === ****************************************************** ===
2025-06-29 20:02:48 INFO: === Algo Trading Bot Started ===
2025-06-29 20:02:48 INFO: Output for processed NIFTY data: processed_files
2025-06-29 20:02:48 INFO: Output for processed Options data: processed_options_files
2025-06-29 20:02:48 INFO: Options input file: User_options_input.csv
2025-06-29 20:02:48 INFO: ============================================================
2025-06-29 20:02:48 INFO: --- Processing Historical NIFTY Data (20:02:48) ---
2025-06-29 20:02:48 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 20:02:48 INFO:  Fetching 13 60min data...
2025-06-29 20:02:48 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:48 INFO:  Fetched 293 new records
2025-06-29 20:02:48 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:02:48 INFO:  Fetching 13 dailymin data...
2025-06-29 20:02:48 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:02:48 INFO:  Fetched 248 new records
2025-06-29 20:02:48 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:02:48 INFO:  Fetching 13 1min data...
2025-06-29 20:02:48 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:48 INFO:  Fetched 2624 new records
2025-06-29 20:02:49 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:02:49 INFO:  Fetching 13 5min data...
2025-06-29 20:02:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:49 INFO:  Fetched 1124 new records
2025-06-29 20:02:49 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:02:49 INFO:  Fetching 25 60min data...
2025-06-29 20:02:49 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:49 INFO:  Fetched 293 new records
2025-06-29 20:02:50 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:02:50 INFO:  Fetching 25 dailymin data...
2025-06-29 20:02:50 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:02:50 INFO:  Fetched 248 new records
2025-06-29 20:02:50 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:02:50 INFO:  Fetching 25 1min data...
2025-06-29 20:02:50 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:50 INFO:  Fetched 2624 new records
2025-06-29 20:02:50 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:02:50 INFO:  Fetching 25 5min data...
2025-06-29 20:02:51 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:51 INFO:  Fetched 1124 new records
2025-06-29 20:02:51 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:02:51 INFO:  Fetching 27 60min data...
2025-06-29 20:02:51 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:51 INFO:  Fetched 293 new records
2025-06-29 20:02:51 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:02:51 INFO:  Fetching 27 dailymin data...
2025-06-29 20:02:51 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:02:51 INFO:  Fetched 248 new records
2025-06-29 20:02:51 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:02:51 INFO:  Fetching 27 1min data...
2025-06-29 20:02:52 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:52 INFO:  Fetched 2624 new records
2025-06-29 20:02:52 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:02:52 INFO:  Fetching 27 5min data...
2025-06-29 20:02:52 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:02:52 INFO:  Fetched 1124 new records
2025-06-29 20:02:52 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:02:52 INFO: State: SCANNING for trade signals.
2025-06-29 20:02:52 INFO: Entering function: look_for_trade_signals
2025-06-29 20:02:52 INFO: 1-min latest crossover: downward
2025-06-29 20:02:52 INFO: 5-min latest crossover: downward
2025-06-29 20:02:52 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 20:02:52 INFO: ============================================================
2025-06-29 20:02:52 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:02:52 INFO: ============================================================

2025-06-29 20:03:12 INFO: --- Processing Historical NIFTY Data (20:03:12) ---
2025-06-29 20:03:12 INFO:  Fetching 13 1min data...
2025-06-29 20:03:13 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:03:13 INFO:  Fetched 2624 new records
2025-06-29 20:03:13 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:03:13 INFO:  Fetching 13 5min data...
2025-06-29 20:03:13 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:03:13 INFO:  Fetched 1124 new records
2025-06-29 20:03:13 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:03:13 INFO:  Fetching 25 1min data...
2025-06-29 20:03:14 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:03:14 INFO:  Fetched 2624 new records
2025-06-29 20:03:14 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:03:14 INFO:  Fetching 25 5min data...
2025-06-29 20:03:14 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:03:14 INFO:  Fetched 1124 new records
2025-06-29 20:03:15 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:03:15 INFO:  Fetching 27 1min data...
2025-06-29 20:03:15 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:03:15 INFO:  Fetched 2624 new records
2025-06-29 20:03:15 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:03:15 INFO:  Fetching 27 5min data...
2025-06-29 20:03:15 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:03:15 INFO:  Fetched 1124 new records
2025-06-29 20:03:16 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:03:16 INFO: State: SCANNING for trade signals.
2025-06-29 20:03:16 INFO: Entering function: look_for_trade_signals
2025-06-29 20:03:16 INFO: 1-min latest crossover: downward
2025-06-29 20:03:16 INFO: 5-min latest crossover: downward
2025-06-29 20:03:16 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 20:03:16 INFO: ============================================================
2025-06-29 20:03:16 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:03:16 INFO: ============================================================

2025-06-29 20:08:07 INFO: === ****************************************************** ===
2025-06-29 20:08:07 INFO: === ****************START LOGGING* (20:08:07) ************ ===
2025-06-29 20:08:07 INFO: === ****************************************************** ===
2025-06-29 20:08:07 INFO: === Algo Trading Bot Started ===
2025-06-29 20:08:07 INFO: Output for processed NIFTY data: processed_files
2025-06-29 20:08:07 INFO: Output for processed Options data: processed_options_files
2025-06-29 20:08:07 INFO: Options input file: User_options_input.csv
2025-06-29 20:08:07 INFO: ============================================================
2025-06-29 20:08:07 INFO: --- Processing Historical NIFTY Data (20:08:07) ---
2025-06-29 20:08:07 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 20:08:07 INFO:  Fetching 13 1min data...
2025-06-29 20:08:07 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:07 INFO:  Fetched 2624 new records
2025-06-29 20:08:08 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:08:08 INFO:  Fetching 13 5min data...
2025-06-29 20:08:08 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:08 INFO:  Fetched 1124 new records
2025-06-29 20:08:08 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:08:08 INFO:  Fetching 13 60min data...
2025-06-29 20:08:08 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:08 INFO:  Fetched 293 new records
2025-06-29 20:08:09 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:08:09 INFO:  Fetching 13 dailymin data...
2025-06-29 20:08:09 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:08:09 INFO:  Fetched 248 new records
2025-06-29 20:08:09 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:08:09 INFO:  Fetching 25 1min data...
2025-06-29 20:08:09 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:09 INFO:  Fetched 2624 new records
2025-06-29 20:08:10 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:08:10 INFO:  Fetching 25 5min data...
2025-06-29 20:08:10 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:10 INFO:  Fetched 1124 new records
2025-06-29 20:08:10 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:08:10 INFO:  Fetching 25 60min data...
2025-06-29 20:08:10 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:10 INFO:  Fetched 293 new records
2025-06-29 20:08:10 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:08:10 INFO:  Fetching 25 dailymin data...
2025-06-29 20:08:10 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:08:10 INFO:  Fetched 248 new records
2025-06-29 20:08:10 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:08:10 INFO:  Fetching 27 1min data...
2025-06-29 20:08:11 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:11 INFO:  Fetched 2624 new records
2025-06-29 20:08:11 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:08:11 INFO:  Fetching 27 5min data...
2025-06-29 20:08:11 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:11 INFO:  Fetched 1124 new records
2025-06-29 20:08:11 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:08:11 INFO:  Fetching 27 60min data...
2025-06-29 20:08:12 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:12 INFO:  Fetched 293 new records
2025-06-29 20:08:12 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:08:12 INFO:  Fetching 27 dailymin data...
2025-06-29 20:08:12 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:08:12 INFO:  Fetched 248 new records
2025-06-29 20:08:12 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:08:12 INFO: State: SCANNING for trade signals.
2025-06-29 20:08:12 INFO: Entering function: look_for_trade_signals
2025-06-29 20:08:12 INFO: 1-min latest crossover: downward
2025-06-29 20:08:12 INFO: 5-min latest crossover: downward
2025-06-29 20:08:12 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 20:08:12 INFO: ============================================================
2025-06-29 20:08:12 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:08:12 INFO: ============================================================

2025-06-29 20:08:32 INFO: --- Processing Historical NIFTY Data (20:08:32) ---
2025-06-29 20:08:32 INFO:  Fetching 13 1min data...
2025-06-29 20:08:32 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:32 INFO:  Fetched 2624 new records
2025-06-29 20:08:33 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:08:33 INFO:  Fetching 13 5min data...
2025-06-29 20:08:33 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:33 INFO:  Fetched 1124 new records
2025-06-29 20:08:33 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:08:33 INFO:  Fetching 25 1min data...
2025-06-29 20:08:33 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:33 INFO:  Fetched 2624 new records
2025-06-29 20:08:34 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:08:34 INFO:  Fetching 25 5min data...
2025-06-29 20:08:34 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:34 INFO:  Fetched 1124 new records
2025-06-29 20:08:34 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:08:34 INFO:  Fetching 27 1min data...
2025-06-29 20:08:35 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:35 INFO:  Fetched 2624 new records
2025-06-29 20:08:35 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:08:35 INFO:  Fetching 27 5min data...
2025-06-29 20:08:35 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:08:35 INFO:  Fetched 1124 new records
2025-06-29 20:08:35 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:08:35 INFO: State: SCANNING for trade signals.
2025-06-29 20:08:35 INFO: Entering function: look_for_trade_signals
2025-06-29 20:08:35 INFO: 1-min latest crossover: downward
2025-06-29 20:08:35 INFO: 5-min latest crossover: downward
2025-06-29 20:08:35 INFO: Exiting function: look_for_trade_signals (no signal found)
2025-06-29 20:08:35 INFO: ============================================================
2025-06-29 20:08:35 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:08:35 INFO: ============================================================

2025-06-29 20:33:49 INFO: === ****************************************************** ===
2025-06-29 20:33:49 INFO: === ****************START LOGGING* (20:33:49) ************ ===
2025-06-29 20:33:49 INFO: === ****************************************************** ===
2025-06-29 20:33:49 INFO: === Algo Trading Bot Started ===
2025-06-29 20:33:49 INFO: Output for processed NIFTY data: processed_files
2025-06-29 20:33:49 INFO: Output for processed Options data: processed_options_files
2025-06-29 20:33:49 INFO: Options input file: User_options_input.csv
2025-06-29 20:33:49 INFO: ============================================================
2025-06-29 20:33:49 INFO: --- Processing Historical NIFTY Data (20:33:49) ---
2025-06-29 20:33:49 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 20:33:49 INFO:  Fetching 13 5min data...
2025-06-29 20:33:49 INFO:  Fetching 25 5min data...
2025-06-29 20:33:49 INFO:  Fetching 27 5min data...
2025-06-29 20:33:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:49 INFO:  Fetched 1124 new records
2025-06-29 20:33:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:49 INFO:  Fetched 1124 new records
2025-06-29 20:33:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:49 INFO:  Fetched 1124 new records
2025-06-29 20:33:50 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:33:50 INFO:  Fetching 25 1min data...
2025-06-29 20:33:50 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:33:50 INFO:  Fetching 13 1min data...
2025-06-29 20:33:50 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:33:50 INFO:  Fetching 27 1min data...
2025-06-29 20:33:50 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:50 INFO:  Fetched 2624 new records
2025-06-29 20:33:50 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:50 INFO:  Fetched 2624 new records
2025-06-29 20:33:50 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:50 INFO:  Fetched 2624 new records
2025-06-29 20:33:52 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO:  Fetching 25 dailymin data...
2025-06-29 20:33:52 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO:  Fetching 13 dailymin data...
2025-06-29 20:33:52 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO:  Fetching 27 dailymin data...
2025-06-29 20:33:52 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:33:52 INFO:  Fetched 248 new records
2025-06-29 20:33:52 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO:  Fetching 25 60min data...
2025-06-29 20:33:52 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:33:52 INFO:  Fetched 248 new records
2025-06-29 20:33:52 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:33:52 INFO:  Fetched 248 new records
2025-06-29 20:33:52 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO:  Saved processed file: FINNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO:  Fetching 13 60min data...
2025-06-29 20:33:52 INFO:  Fetching 27 60min data...
2025-06-29 20:33:52 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:52 INFO:  Fetched 293 new records
2025-06-29 20:33:52 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:33:52 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:33:52 ERROR:  No data available for FINNIFTY 60min interval
2025-06-29 20:33:52 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:33:52 INFO:  Fetched 293 new records
2025-06-29 20:33:52 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:33:52 INFO: State: SCANNING for trade signals.
2025-06-29 20:33:52 INFO: Entering function: look_for_trade_signals
2025-06-29 20:33:52 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:33:52 INFO: ============================================================
2025-06-29 20:33:52 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:33:52 INFO: ============================================================

2025-06-29 20:34:12 INFO: --- Processing Historical NIFTY Data (20:34:12) ---
2025-06-29 20:34:12 INFO:  Fetching 13 5min data...
2025-06-29 20:34:12 INFO:  Fetching 25 5min data...
2025-06-29 20:34:12 INFO:  Fetching 27 5min data...
2025-06-29 20:34:12 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:34:12 INFO:  Fetched 1124 new records
2025-06-29 20:34:12 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:34:12 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:34:12 INFO:  Fetched 1124 new records
2025-06-29 20:34:12 INFO:  Fetched 1124 new records
2025-06-29 20:34:13 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:34:13 INFO:  Fetching 25 1min data...
2025-06-29 20:34:13 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:34:13 INFO:  Fetching 27 1min data...
2025-06-29 20:34:13 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:34:13 INFO:  Fetching 13 1min data...
2025-06-29 20:34:13 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:34:13 ERROR:  No data available for NIFTY 1min interval
2025-06-29 20:34:13 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:34:13 INFO:  Fetched 2624 new records
2025-06-29 20:34:13 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:34:13 INFO:  Fetched 2624 new records
2025-06-29 20:34:14 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:34:14 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:34:14 INFO: State: SCANNING for trade signals.
2025-06-29 20:34:14 INFO: Entering function: look_for_trade_signals
2025-06-29 20:34:14 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:34:14 INFO: ============================================================
2025-06-29 20:34:14 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:34:14 INFO: ============================================================

2025-06-29 20:39:42 INFO: === ****************************************************** ===
2025-06-29 20:39:42 INFO: === ****************START LOGGING* (20:39:42) ************ ===
2025-06-29 20:39:42 INFO: === ****************************************************** ===
2025-06-29 20:39:42 INFO: === Algo Trading Bot Started ===
2025-06-29 20:39:42 INFO: Output for processed NIFTY data: processed_files
2025-06-29 20:39:42 INFO: Output for processed Options data: processed_options_files
2025-06-29 20:39:42 INFO: Options input file: User_options_input.csv
2025-06-29 20:39:42 INFO: ============================================================
2025-06-29 20:39:42 INFO: --- Processing Historical NIFTY Data (20:39:42) ---
2025-06-29 20:39:42 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-29 20:39:42 INFO: Starting ThreadPoolExecutor with 3 threads
2025-06-29 20:39:42 INFO: [MainThread] Submitting job for NIFTY
2025-06-29 20:39:42 INFO: [ThreadPoolExecutor-0_0] Processing NIFTY
2025-06-29 20:39:42 INFO:  Fetching 13 60min data...
2025-06-29 20:39:42 INFO: [MainThread] Submitting job for BANKNIFTY
2025-06-29 20:39:42 INFO: [ThreadPoolExecutor-0_1] Processing BANKNIFTY
2025-06-29 20:39:42 INFO:  Fetching 25 60min data...
2025-06-29 20:39:42 INFO: [MainThread] Submitting job for FINNIFTY
2025-06-29 20:39:42 INFO: [ThreadPoolExecutor-0_2] Processing FINNIFTY
2025-06-29 20:39:42 INFO:  Fetching 27 60min data...
2025-06-29 20:39:42 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:42 INFO:  Fetched 293 new records
2025-06-29 20:39:42 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:42 INFO:  Fetched 293 new records
2025-06-29 20:39:42 INFO: Data fetched from 2025-04-30 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:42 INFO:  Fetched 293 new records
2025-06-29 20:39:42 INFO:  Saved processed file: NIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:39:42 INFO:  Fetching 13 5min data...
2025-06-29 20:39:42 INFO:  Saved processed file: BANKNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:39:42 INFO:  Fetching 25 5min data...
2025-06-29 20:39:42 INFO:  Saved processed file: FINNIFTY_60min_2025-04-30_to_2025-06-29_processed.csv
2025-06-29 20:39:42 INFO:  Fetching 27 5min data...
2025-06-29 20:39:42 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:39:42 ERROR:  No data available for FINNIFTY 5min interval
2025-06-29 20:39:42 INFO:  Fetching 27 1min data...
2025-06-29 20:39:43 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:43 INFO:  Fetched 1124 new records
2025-06-29 20:39:43 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:43 INFO:  Fetched 1124 new records
2025-06-29 20:39:43 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:39:43 ERROR:  No data available for FINNIFTY 1min interval
2025-06-29 20:39:43 INFO:  Fetching 27 dailymin data...
2025-06-29 20:39:43 ERROR: [MainThread] Exception in thread: If using all scalar values, you must pass an index
2025-06-29 20:39:43 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:39:43 INFO:  Fetching 25 1min data...
2025-06-29 20:39:43 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:39:43 INFO:  Fetching 13 1min data...
2025-06-29 20:39:43 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:43 INFO:  Fetched 2624 new records
2025-06-29 20:39:43 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:39:43 INFO:  Fetched 2624 new records
2025-06-29 20:39:44 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:39:44 INFO:  Fetching 25 dailymin data...
2025-06-29 20:39:44 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:39:44 INFO:  Fetching 13 dailymin data...
2025-06-29 20:39:44 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:39:44 INFO:  Fetched 248 new records
2025-06-29 20:39:45 INFO: Data fetched from 2024-06-29 to 2025-06-29
2025-06-29 20:39:45 INFO:  Fetched 248 new records
2025-06-29 20:39:45 INFO:  Saved processed file: BANKNIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:39:45 INFO: [MainThread] One instrument job completed.
2025-06-29 20:39:45 INFO:  Saved processed file: NIFTY_daily_2024-06-29_to_2025-06-29_processed.csv
2025-06-29 20:39:45 INFO: [MainThread] One instrument job completed.
2025-06-29 20:39:45 INFO: State: SCANNING for trade signals.
2025-06-29 20:39:45 INFO: Entering function: look_for_trade_signals
2025-06-29 20:39:45 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:39:45 INFO: ============================================================
2025-06-29 20:39:45 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:39:45 INFO: ============================================================

2025-06-29 20:40:05 INFO: --- Processing Historical NIFTY Data (20:40:05) ---
2025-06-29 20:40:05 INFO: Starting ThreadPoolExecutor with 3 threads
2025-06-29 20:40:05 INFO: [MainThread] Submitting job for NIFTY
2025-06-29 20:40:05 INFO: [ThreadPoolExecutor-1_0] Processing NIFTY
2025-06-29 20:40:05 INFO:  Fetching 13 5min data...
2025-06-29 20:40:05 INFO: [MainThread] Submitting job for BANKNIFTY
2025-06-29 20:40:05 INFO: [ThreadPoolExecutor-1_1] Processing BANKNIFTY
2025-06-29 20:40:05 INFO: [MainThread] Submitting job for FINNIFTY
2025-06-29 20:40:05 INFO:  Fetching 25 5min data...
2025-06-29 20:40:05 INFO: [ThreadPoolExecutor-1_2] Processing FINNIFTY
2025-06-29 20:40:05 INFO:  Fetching 27 5min data...
2025-06-29 20:40:05 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:05 INFO:  Fetched 1124 new records
2025-06-29 20:40:05 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:05 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:05 INFO:  Fetched 1124 new records
2025-06-29 20:40:05 INFO:  Fetched 1124 new records
2025-06-29 20:40:05 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:05 INFO:  Fetching 27 1min data...
2025-06-29 20:40:05 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:05 INFO:  Fetching 13 1min data...
2025-06-29 20:40:06 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:06 INFO:  Fetching 25 1min data...
2025-06-29 20:40:06 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:06 INFO:  Fetched 2624 new records
2025-06-29 20:40:06 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:40:06 ERROR:  No data available for BANKNIFTY 1min interval
2025-06-29 20:40:06 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:06 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:06 INFO:  Fetched 2624 new records
2025-06-29 20:40:07 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:40:07 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:07 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:40:07 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:07 INFO: State: SCANNING for trade signals.
2025-06-29 20:40:07 INFO: Entering function: look_for_trade_signals
2025-06-29 20:40:07 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:40:07 INFO: ============================================================
2025-06-29 20:40:07 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:40:07 INFO: ============================================================

2025-06-29 20:40:27 INFO: --- Processing Historical NIFTY Data (20:40:27) ---
2025-06-29 20:40:27 INFO: Starting ThreadPoolExecutor with 3 threads
2025-06-29 20:40:27 INFO: [MainThread] Submitting job for NIFTY
2025-06-29 20:40:27 INFO: [ThreadPoolExecutor-2_0] Processing NIFTY
2025-06-29 20:40:27 INFO:  Fetching 13 5min data...
2025-06-29 20:40:27 INFO: [MainThread] Submitting job for BANKNIFTY
2025-06-29 20:40:27 INFO: [ThreadPoolExecutor-2_1] Processing BANKNIFTY
2025-06-29 20:40:27 INFO: [MainThread] Submitting job for FINNIFTY
2025-06-29 20:40:27 INFO:  Fetching 25 5min data...
2025-06-29 20:40:27 INFO: [ThreadPoolExecutor-2_2] Processing FINNIFTY
2025-06-29 20:40:27 INFO:  Fetching 27 5min data...
2025-06-29 20:40:27 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:27 INFO:  Fetched 1124 new records
2025-06-29 20:40:27 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:27 INFO:  Fetched 1124 new records
2025-06-29 20:40:27 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:27 INFO:  Fetched 1124 new records
2025-06-29 20:40:27 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:27 INFO:  Fetching 13 1min data...
2025-06-29 20:40:28 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:28 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:28 INFO:  Fetching 27 1min data...
2025-06-29 20:40:28 INFO:  Fetching 25 1min data...
2025-06-29 20:40:28 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:28 INFO:  Fetched 2624 new records
2025-06-29 20:40:28 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:40:28 ERROR:  No data available for BANKNIFTY 1min interval
2025-06-29 20:40:28 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:28 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:28 INFO:  Fetched 2624 new records
2025-06-29 20:40:29 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:40:29 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:29 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:40:29 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:29 INFO: State: SCANNING for trade signals.
2025-06-29 20:40:29 INFO: Entering function: look_for_trade_signals
2025-06-29 20:40:29 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:40:29 INFO: ============================================================
2025-06-29 20:40:29 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:40:29 INFO: ============================================================

2025-06-29 20:40:49 INFO: --- Processing Historical NIFTY Data (20:40:49) ---
2025-06-29 20:40:49 INFO: Starting ThreadPoolExecutor with 3 threads
2025-06-29 20:40:49 INFO: [MainThread] Submitting job for NIFTY
2025-06-29 20:40:49 INFO: [ThreadPoolExecutor-3_0] Processing NIFTY
2025-06-29 20:40:49 INFO:  Fetching 13 5min data...
2025-06-29 20:40:49 INFO: [MainThread] Submitting job for BANKNIFTY
2025-06-29 20:40:49 INFO: [ThreadPoolExecutor-3_1] Processing BANKNIFTY
2025-06-29 20:40:49 INFO: [MainThread] Submitting job for FINNIFTY
2025-06-29 20:40:49 INFO:  Fetching 25 5min data...
2025-06-29 20:40:49 INFO: [ThreadPoolExecutor-3_2] Processing FINNIFTY
2025-06-29 20:40:49 INFO:  Fetching 27 5min data...
2025-06-29 20:40:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:49 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:49 INFO:  Fetched 1124 new records
2025-06-29 20:40:49 INFO:  Fetched 1124 new records
2025-06-29 20:40:49 INFO:  Fetched 1124 new records
2025-06-29 20:40:49 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:49 INFO:  Fetching 27 1min data...
2025-06-29 20:40:50 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:50 INFO:  Fetching 25 1min data...
2025-06-29 20:40:50 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:40:50 INFO:  Fetching 13 1min data...
2025-06-29 20:40:50 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:40:50 ERROR:  No data available for NIFTY 1min interval
2025-06-29 20:40:50 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:50 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:50 INFO:  Fetched 2624 new records
2025-06-29 20:40:50 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:40:50 INFO:  Fetched 2624 new records
2025-06-29 20:40:51 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:40:51 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:51 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:40:51 INFO: [MainThread] One instrument job completed.
2025-06-29 20:40:51 INFO: State: SCANNING for trade signals.
2025-06-29 20:40:51 INFO: Entering function: look_for_trade_signals
2025-06-29 20:40:51 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:40:51 INFO: ============================================================
2025-06-29 20:40:51 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:40:51 INFO: ============================================================

2025-06-29 20:41:11 INFO: --- Processing Historical NIFTY Data (20:41:11) ---
2025-06-29 20:41:11 INFO: Starting ThreadPoolExecutor with 3 threads
2025-06-29 20:41:11 INFO: [MainThread] Submitting job for NIFTY
2025-06-29 20:41:11 INFO: [ThreadPoolExecutor-4_0] Processing NIFTY
2025-06-29 20:41:11 INFO: [MainThread] Submitting job for BANKNIFTY
2025-06-29 20:41:11 INFO:  Fetching 13 5min data...
2025-06-29 20:41:11 INFO: [ThreadPoolExecutor-4_1] Processing BANKNIFTY
2025-06-29 20:41:11 INFO: [MainThread] Submitting job for FINNIFTY
2025-06-29 20:41:11 INFO:  Fetching 25 5min data...
2025-06-29 20:41:11 INFO: [ThreadPoolExecutor-4_2] Processing FINNIFTY
2025-06-29 20:41:11 INFO:  Fetching 27 5min data...
2025-06-29 20:41:11 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:11 INFO:  Fetched 1124 new records
2025-06-29 20:41:11 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:11 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:11 INFO:  Fetched 1124 new records
2025-06-29 20:41:11 INFO:  Fetched 1124 new records
2025-06-29 20:41:12 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:41:12 INFO:  Fetching 25 1min data...
2025-06-29 20:41:12 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:41:12 INFO:  Fetching 13 1min data...
2025-06-29 20:41:12 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:41:12 INFO:  Fetching 27 1min data...
2025-06-29 20:41:12 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:12 INFO:  Fetched 2624 new records
2025-06-29 20:41:12 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:41:12 ERROR:  No data available for FINNIFTY 1min interval
2025-06-29 20:41:12 INFO: [MainThread] One instrument job completed.
2025-06-29 20:41:12 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:12 INFO:  Fetched 2624 new records
2025-06-29 20:41:13 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:41:13 INFO: [MainThread] One instrument job completed.
2025-06-29 20:41:13 INFO:  Saved processed file: BANKNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:41:13 INFO: [MainThread] One instrument job completed.
2025-06-29 20:41:13 INFO: State: SCANNING for trade signals.
2025-06-29 20:41:13 INFO: Entering function: look_for_trade_signals
2025-06-29 20:41:13 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:41:13 INFO: ============================================================
2025-06-29 20:41:13 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:41:13 INFO: ============================================================

2025-06-29 20:41:33 INFO: --- Processing Historical NIFTY Data (20:41:33) ---
2025-06-29 20:41:33 INFO: Starting ThreadPoolExecutor with 3 threads
2025-06-29 20:41:33 INFO: [MainThread] Submitting job for NIFTY
2025-06-29 20:41:33 INFO: [ThreadPoolExecutor-5_0] Processing NIFTY
2025-06-29 20:41:33 INFO: [MainThread] Submitting job for BANKNIFTY
2025-06-29 20:41:33 INFO:  Fetching 13 5min data...
2025-06-29 20:41:33 INFO: [ThreadPoolExecutor-5_1] Processing BANKNIFTY
2025-06-29 20:41:33 INFO: [MainThread] Submitting job for FINNIFTY
2025-06-29 20:41:33 INFO:  Fetching 25 5min data...
2025-06-29 20:41:33 INFO: [ThreadPoolExecutor-5_2] Processing FINNIFTY
2025-06-29 20:41:33 INFO:  Fetching 27 5min data...
2025-06-29 20:41:33 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:33 INFO:  Fetched 1124 new records
2025-06-29 20:41:33 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:33 INFO:  Fetched 1124 new records
2025-06-29 20:41:33 INFO: Data fetched from 2025-06-09 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:33 INFO:  Fetched 1124 new records
2025-06-29 20:41:34 INFO:  Saved processed file: NIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:41:34 INFO:  Fetching 13 1min data...
2025-06-29 20:41:34 INFO:  Saved processed file: FINNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:41:34 INFO:  Fetching 27 1min data...
2025-06-29 20:41:34 INFO:  Saved processed file: BANKNIFTY_5min_2025-06-09_to_2025-06-29_processed.csv
2025-06-29 20:41:34 INFO:  Fetching 25 1min data...
2025-06-29 20:41:34 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:34 INFO:  Fetched 2624 new records
2025-06-29 20:41:34 ERROR: Unexpected response structure. Cannot create DataFrame.
2025-06-29 20:41:34 ERROR:  No data available for BANKNIFTY 1min interval
2025-06-29 20:41:34 INFO: [MainThread] One instrument job completed.
2025-06-29 20:41:34 INFO: Data fetched from 2025-06-19 09:15:00 to 2025-06-29 15:29:00
2025-06-29 20:41:34 INFO:  Fetched 2624 new records
2025-06-29 20:41:35 INFO:  Saved processed file: NIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:41:35 INFO: [MainThread] One instrument job completed.
2025-06-29 20:41:35 INFO:  Saved processed file: FINNIFTY_1min_2025-06-19_to_2025-06-29_processed.csv
2025-06-29 20:41:35 INFO: [MainThread] One instrument job completed.
2025-06-29 20:41:35 INFO: State: SCANNING for trade signals.
2025-06-29 20:41:35 INFO: Entering function: look_for_trade_signals
2025-06-29 20:41:35 INFO: Exiting look_for_trade_signals: Input DataFrame is empty.
2025-06-29 20:41:35 INFO: ============================================================
2025-06-29 20:41:35 INFO: Current State: SCANNING. Next update in 20 seconds...
2025-06-29 20:41:35 INFO: ============================================================

