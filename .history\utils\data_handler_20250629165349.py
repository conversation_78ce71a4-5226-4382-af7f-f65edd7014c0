# data_handler.py

import os
import datetime
import pandas as pd
import requests
import time
from dhanhq import dhanhq
import logging

import config.config as config
from utils.processing_functions import process_dataframe

# Global variable for instrument data, managed within this module
instrument_df = None

def get_instrument_file():
    """
    Get instrument file from Dhan API or local cache.
    """
    global instrument_df

    try:
        current_date = time.strftime("%Y-%m-%d")
        expected_file = f'all_instrument {current_date}.csv'
        
        os.makedirs(config.DEPENDENCIES_DIR, exist_ok=True)

        # Clean up old instrument files
        if os.path.exists(config.DEPENDENCIES_DIR):
            for item in os.listdir(config.DEPENDENCIES_DIR):
                if item.startswith('all_instrument') and current_date not in item:
                    file_path = os.path.join(config.DEPENDENCIES_DIR, item)
                    if os.path.isfile(file_path):
                        try:
                            os.remove(file_path)
                            logging.info(f"  Removed old instrument file: {item}")
                        except Exception as e:
                            print(f"  ✗ Error removing old file {item}: {str(e)}")

        expected_file_path = os.path.join(config.DEPENDENCIES_DIR, expected_file)
        if os.path.exists(expected_file_path):
            try:
                logging.info(f"  Reading existing instrument file: {expected_file}")
                instrument_df = pd.read_csv(expected_file_path, low_memory=False)
                return instrument_df
            except Exception as e:
                print(f"  ✗ Error reading existing instrument file: {str(e)}")
                logging.error(f"  ✗ Error reading existing instrument file: {str(e)}")

        instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

        try:
            instrument_df.to_csv(expected_file_path, index=False)
            logging.info(f"  ✓ Saved instrument file: {expected_file}")
        except Exception as e:
            print(f"  ✗ Error saving instrument file: {str(e)}")
            logging.error(f"  ✗ Error saving instrument file: {str(e)}")

        return instrument_df

    except Exception as e:
        print(f"  ✗ Error in get_instrument_file: {str(e)}")
        logging.error(f"  ✗ Error in get_instrument_file: {str(e)}")
        return None


def get_option_security_details(tradingsymbol, exchange):
    """
    Get security details for option symbols from instrument data.
    """
    global instrument_df

    try:
        if instrument_df is None:
            instrument_df = get_instrument_file()

        if instrument_df is None:
            logging.error(f"  ✗ Instrument data not available for {tradingsymbol}")
            return None, None

        instrument_exchange = {
            'NSE': 'NSE', 'BSE': 'BSE', 'NFO': 'NSE',
            'BFO': 'BSE', 'MCX': 'MCX', 'CUR': 'NSE'
        }
        index_exchange = {
            "NIFTY": 'NSE', "BANKNIFTY": "NSE", "FINNIFTY": "NSE",
            "MIDCPNIFTY": "NSE", "BANKEX": "BSE", "SENSEX": "BSE"
        }

        if tradingsymbol in index_exchange:
            exchange = index_exchange[tradingsymbol]

        filtered_df = instrument_df[
            ((instrument_df['SEM_TRADING_SYMBOL'] == tradingsymbol) |
             (instrument_df['SEM_CUSTOM_SYMBOL'] == tradingsymbol)) &
            (instrument_df['SEM_EXM_EXCH_ID'] == instrument_exchange[exchange])
        ]

        if filtered_df.empty:
            logging.error(f"  ✗ No instrument found for {tradingsymbol} on {exchange}")
            return None, None

        last_record = filtered_df.iloc[-1]
        security_id = last_record['SEM_SMST_SECURITY_ID']
        instrument_type = last_record['SEM_INSTRUMENT_NAME']

        return security_id, instrument_type
    except Exception as e:
        logging.error(f"  ✗ Error getting security details for {tradingsymbol}: {str(e)}")
        return None, None


def fetch_historical_data(security_id, exchange_segment, instrument_type, interval):
    """
    Fetch historical data for NIFTY Index from Dhan API based on interval.
    """
    # security_id = config.NIFTY_SECURITY_ID
    # exchange_segment = config.NIFTY_EXCHANGE_SEGMENT
    # instrument_type = config.NIFTY_INSTRUMENT_TYPE
    if not security_id or not exchange_segment or not instrument_type:
        logging.error(" Invalid security details provided.")
        return None, None, None
    
    now = datetime.datetime.now()
    today = now.date()
    
    if now.time() < config.MARKET_START:
        end_date = today - datetime.timedelta(days=1)
        while end_date.weekday() > 4:
            end_date -= datetime.timedelta(days=1)
    else:
        end_date = today

    if interval == "1":
        from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "5":
        from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "60":
        from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "daily":
        from_date = (end_date - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    to_date = end_date.strftime("%Y-%m-%d 15:29:00") if interval != "daily" else end_date.strftime("%Y-%m-%d")

    headers = {
        "access-token": config.TOKEN,
        "Content-Type": "application/json"
    }

    if interval == "daily":
        client = dhanhq(config.CLIENT_CODE, config.TOKEN)
        data = client.historical_daily_data(
            security_id=security_id,
            exchange_segment=exchange_segment,
            instrument_type=instrument_type,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
    else:
        payload = {
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "instrument": instrument_type,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()
    
    print("Fetch",)

    if not isinstance(data, dict):
        logging.error("Unexpected API response format. Not a dictionary.")
        return None, None, None

    df = None
    if "data" in data:
        if interval != "daily":
            chart_data = data["data"]
            df = pd.DataFrame({
                "open": chart_data["open"], "high": chart_data["high"],
                "low": chart_data["low"], "close": chart_data["close"],
                "volume": chart_data["volume"],
                "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
            })
        else:
            df = pd.DataFrame(data["data"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    elif all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
        df = pd.DataFrame(data)
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    else:
        logging.error("Unexpected response structure. Cannot create DataFrame.")
        return None, from_date[:10], to_date[:10]

    IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

    if interval != "daily":
        df = df[df["timestamp"].dt.time <= config.MARKET_CLOSE]

    df = df[["timestamp", "open", "high", "low", "close", "volume"]]
    
    logging.info(f"Data fetched from {from_date} to {to_date}")
    return df, from_date[:10], to_date[:10]


def fetch_option_data(security_id, exchange_segment, instrument_type, interval, tradingsymbol):
    """
    Fetch option data from Dhan API for a specific security and interval.
    """
    try:
        now = datetime.datetime.now()
        today = now.date()

        if now.time() < config.MARKET_START:
            end_date = today - datetime.timedelta(days=1)
            while end_date.weekday() > 4:
                end_date -= datetime.timedelta(days=1)
        else:
            end_date = today

        if interval == "1":
            from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d")
        elif interval == "5":
            from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d")
        elif interval == "60":
            from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d")
        else:
            raise ValueError(f"Invalid interval for options: {interval}")

        to_date = end_date.strftime("%Y-%m-%d")

        client = dhanhq(config.CLIENT_CODE, config.TOKEN)
        response = client.intraday_minute_data(
            str(security_id), exchange_segment, instrument_type,
            from_date, to_date, int(interval)
        )

        if not response or 'data' not in response:
            logging.error(f"  ✗ No data received for {tradingsymbol}")
            return None, None, None

        df = pd.DataFrame(response['data'])
        if df.empty:
            logging.error(f"  ✗ Empty data received for {tradingsymbol}")
            return None, None, None

        df['timestamp'] = df['timestamp'].apply(lambda x: client.convert_to_date_time(x))
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            df['timestamp'] = pd.to_datetime(df['timestamp'])

        if df['timestamp'].dt.tz is None:
            IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
            df['timestamp'] = df['timestamp'].dt.tz_localize(IST)

        df = df[df['timestamp'].dt.time <= config.MARKET_CLOSE]

        expected_columns = ["timestamp", "open", "high", "low", "close", "volume"]
        available_columns = [col for col in expected_columns if col in df.columns]
        df = df[available_columns]
        
        return df, from_date, to_date

    except Exception as e:
        logging.error(f"  ✗ Error fetching option data for {tradingsymbol}: {str(e)}")
        return None, None, None


def process_options_csv():
    """
    Process all entries in User_options_input.csv and fetch option data for all timeframes.
    """
    try:
        if not os.path.exists(config.OPTIONS_CSV_FILE):
            logging.error(f"  ✗ Options CSV file not found: {config.OPTIONS_CSV_FILE}")
            return {"success": False, "error": "CSV file not found"}

        user_df = pd.read_csv(config.OPTIONS_CSV_FILE)
        if user_df.empty:
            logging.error(f"  ✗ Options CSV file is empty")
            return {"success": False, "error": "CSV file is empty"}

        os.makedirs(config.OPTIONS_OUTPUT_DIR, exist_ok=True)
        
        global instrument_df
        if instrument_df is None:
            instrument_df = get_instrument_file()

        results = {
            "success": True, "total_entries": len(user_df), "processed_entries": 0,
            "successful_fetches": 0, "failed_fetches": 0, "details": []
        }
        master_dataframes = {}

        for index, row in user_df.iterrows():
            try:
                display_name = row['DISPLAY_NAME']
                if display_name not in master_dataframes:
                    master_dataframes[display_name] = {"Option_symbol": display_name}

                exchange = 'NFO'
                security_id, instrument_type = get_option_security_details(display_name, exchange)

                if security_id is None or instrument_type is None:
                    logging.error(f"  ✗ Could not find security details for {display_name}")
                    results["failed_fetches"] += 1
                    results["details"].append({"symbol": display_name, "status": "failed", "error": "Security details not found"})
                    continue

                client = dhanhq(config.CLIENT_CODE, config.TOKEN)
                exchange_segment = client.FNO

                entry_results = {"symbol": display_name, "timeframes": {}}
                for interval in config.OPTION_TIMEFRAMES:
                    try:
                        df, from_date, to_date = fetch_option_data(
                            security_id, exchange_segment, instrument_type, interval, display_name
                        )
                        if df is not None and not df.empty:
                            df_processed = process_dataframe(df)
                            master_dataframes[display_name][f"df_opt_{interval}min"] = df_processed

                            clean_symbol = display_name.replace(" ", "_").replace("/", "_")
                            interval_suffix = f"{interval}min"
                            output_filename = f"{clean_symbol}_{interval_suffix}_{from_date}_to_{to_date}_processed.csv"
                            output_path = os.path.join(config.OPTIONS_OUTPUT_DIR, output_filename)
                            df_processed.to_csv(output_path, index=False)
                            logging.info(f"     Saved {interval}min data: {output_filename}")

                            entry_results["timeframes"][interval] = {"status": "success", "records": len(df_processed), "file": output_filename}
                            results["successful_fetches"] += 1
                        else:
                            logging.error(f"    ✗ No data for {interval}min timeframe")
                            entry_results["timeframes"][interval] = {"status": "failed", "error": "No data received"}
                            results["failed_fetches"] += 1
                    except Exception as e:
                        logging.error(f"    ✗ Error processing {interval}min timeframe: {str(e)}")
                        entry_results["timeframes"][interval] = {"status": "failed", "error": str(e)}
                        results["failed_fetches"] += 1
                
                entry_results["status"] = "processed"
                results["details"].append(entry_results)
                results["processed_entries"] += 1
            except Exception as e:
                logging.error(f"  ✗ Error processing entry {index + 1}: {str(e)}")
                results["failed_fetches"] += 1
                results["details"].append({"symbol": row.get('DISPLAY_NAME', f'Entry {index + 1}'), "status": "failed", "error": str(e)})

        results["dataframes"] = master_dataframes
        return results

    except Exception as e:
        logging.error(f"  ✗ Error in process_options_csv: {str(e)}")
        return {"success": False, "error": str(e)}