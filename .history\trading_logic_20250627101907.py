# trading_logic.py

import datetime
import logging
import os
import pandas as pd

import config

def find_latest_signals(df):
    """
    Analyze a processed option DataFrame to identify the most recent trading signals.
    """
    result = {'latest_crossover': None, 'latest_zone': None}
    if df is None or df.empty:
        return result

    required_cols = ['timestamp', 'crossover_signal', 'Zones_sup_dem', 'Zone_Status']
    if not all(col in df.columns for col in required_cols):
        return result

    try:
        mask_crossover = df['crossover_signal'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover'] = df.iloc[last_idx]
    except Exception:
        pass

    try:
        mask_zone = (df['Zones_sup_dem'].isin(['Supply', 'Demand']) &
                     df['Zones_sup_dem'].notna() &
                     (df['Zones_sup_dem'].astype(str).str.strip() != '') &
                     df['Zone_Status'].isin(['Valid', 'Tested']))
        if mask_zone.any():
            last_idx = df[mask_zone].index[-1]
            result['latest_zone'] = df.iloc[last_idx]
    except Exception:
        pass
    
    return result


def look_for_trade_signals(df_1, df_5):
    """
    Looks for trade signals based on 1-min and 5-min data.
    """
    logging.info("Entering function: look_for_trade_signals")
    if df_1 is None or df_5 is None or df_1.empty or df_5.empty:
        logging.info("Exiting look_for_trade_signals: Input DataFrame is empty.")
        return "None", 'False'

    latest_1min = find_latest_signals(df_1)
    latest_5min = find_latest_signals(df_5)
    
    last_stored_1min_data = df_1.iloc[-1]
    last_stored_5min_data = df_5.iloc[-1]
    
    if (latest_1min['latest_crossover'] is not None and
        latest_1min['latest_crossover']['timestamp'] != last_stored_1min_data['timestamp']):
        
        if (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) > datetime.timedelta(minutes=3): 
            logging.info(f"Crossover happened long back. No Trade {latest_1min['latest_crossover']['timestamp']}")
            return "None", 'False'
        
        if latest_5min['latest_crossover'] is not None:
            logging.info(f"1-min latest crossover: {latest_1min['latest_crossover']['crossover_signal']}")
            logging.info(f"5-min latest crossover: {latest_5min['latest_crossover']['crossover_signal']}")
            
            is_put_signal = (latest_5min['latest_crossover']['crossover_signal'] == 'downward' and
                             latest_1min['latest_crossover']['crossover_signal'] == 'downward' and
                             last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA10"] and 
                             last_stored_5min_data["high"] < last_stored_5min_data["WMA10"])

            is_call_signal = (latest_5min['latest_crossover']['crossover_signal'] == 'upward' and
                              latest_1min['latest_crossover']['crossover_signal'] == 'upward' and
                              last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA10"] and 
                              last_stored_5min_data["low"] > last_stored_5min_data["WMA10"])

            if is_put_signal:
                logging.info("PUT signal identified.")
                return "PUT", 'True'
            
            if is_call_signal:
                logging.info("CALL signal identified.")
                return "CALL", 'True'

    logging.info("Exiting function: look_for_trade_signals (no signal found)")
    return "None", 'False'


def get_info_for_trade(master_dataframes, option_type_to_trade):
    """
    Finds the trading symbol and latest candle for the specified option type.
    """
    logging.info(f"Entering function: Get_info_for_trade for {option_type_to_trade}")
    for symbol in master_dataframes:
        if symbol.split()[-1] == option_type_to_trade:
            logging.info(f"Found matching symbol: {symbol}")
            latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
            return symbol, latest_1min_option_candle
    logging.info("No matching symbol found.")
    return None, None


def take_the_trade(symbol, option_1min_ready_for_trade_candle, master_dataframes):
    """
    Determines if a trade should be taken based on the latest option data.
    """
    logging.info(f"Entering function: take_the_trade for {symbol}")
    latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
    option_type = symbol.split()[-1]

    if option_type == "PUT":
        if latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']:
            logging.info(f"PUT Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['low']}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
        if (latest_1min_option_candle['crossover_signal'] == 'upward' and
            latest_1min_option_candle['timestamp'] > option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"PUT Trade invalidated. Signal reversed.")
            return latest_1min_option_candle['timestamp'], "False"
        
    elif option_type == "CALL":
        if latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']:
            logging.info(f"CALL Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['low']}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
        if (latest_1min_option_candle['crossover_signal'] == 'downward' and
            latest_1min_option_candle['timestamp'] > option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"CALL Trade invalidated. Signal reversed.")
            return latest_1min_option_candle['timestamp'], "False"
    else:
        logging.info(f"No trade taken. Invalid symbol type {symbol}")
        return latest_1min_option_candle['timestamp'], "False"
    
    logging.info("Exiting function: take_the_trade (conditions not met)")
    return None, "Take trade"


def check_for_trade_exit(df_1, trade_symbol, time_trade_taken, master_dataframes, trade_log_df):
    """
    Checks for conditions to exit an ongoing trade.
    """
    logging.info(f"Entering function: check_for_trade_exit for {trade_symbol}")
    latest_1min_option_candle = master_dataframes[trade_symbol]['df_opt_1min'].iloc[-1]
    latest_5min_option_candle = master_dataframes[trade_symbol]['df_opt_5min'].iloc[-1]
    result_1min = find_latest_signals(df_1)
    trade_log_recent_entry = trade_log_df.iloc[-1]
    option_type = trade_symbol.split()[-1]

    # Profit target exit
    if (latest_1min_option_candle['low'] > (trade_log_recent_entry['Trade_entry_price'] + 15)):
        logging.info(f"Exit for profit target: {trade_symbol}")
        return 'Trade Exit'
    
    # Stop loss exit
    if (latest_1min_option_candle['low'] <= (latest_5min_option_candle['WMA10'] - 0.10)):
        logging.info(f"Exit for stop loss: {trade_symbol}")
        return 'Trade Exit'
    
    # Time-based exit
    if (latest_1min_option_candle['timestamp'] - time_trade_taken) > datetime.timedelta(minutes=30):
        logging.info(f"Exit due to time limit: {trade_symbol}")
        return 'Trade Exit'

    # Signal reversal exit
    if option_type == "PUT":
        if (result_1min['latest_crossover'] is not None and
            result_1min['latest_crossover']['crossover_signal'] == 'upward' and
            result_1min['latest_crossover']['timestamp'] > time_trade_taken):
            logging.info("Exit PUT trade due to upward crossover.")
            return 'Trade Exit'
    elif option_type == "CALL":
        if (result_1min['latest_crossover'] is not None and
            result_1min['latest_crossover']['crossover_signal'] == 'downward' and
            result_1min['latest_crossover']['timestamp'] > time_trade_taken):
            logging.info("Exit CALL trade due to downward crossover.")
            return 'Trade Exit'

    logging.info(f"Continue to hold trade: {trade_symbol}")
    return 'Trade Taken'


def exit_the_trade(symbol, time_trade_taken, master_dataframes, trade_log_df, to_date_str):
    """
    Records the exit details of a trade and saves the log.
    """
    logging.info(f"Entering function: exit_the_trade for {symbol}")
    latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
    logging.info(f"{symbol} Trade Exited at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['close']}")
    
    trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'trade_exit_time'] = latest_1min_option_candle['timestamp']
    trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_exit_price'] = latest_1min_option_candle['close']
    
    try:
        user_df = pd.read_csv(config.OPTIONS_CSV_FILE)
        options_lot_size_df = pd.read_csv(config.OPTIONS_LOT_SIZE_CSV)
        
        lot_size = 25 # Default lot size for NIFTY if not found
        underlying_symbol = user_df[user_df['DISPLAY_NAME'] == symbol]['UNDERLYING_SYMBOL'].iloc[0]
        if not options_lot_size_df[options_lot_size_df['SYMBOL'] == underlying_symbol].empty:
            lot_size = options_lot_size_df[options_lot_size_df['SYMBOL'] == underlying_symbol]['LOT_SIZE'].iloc[0]

        entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_entry_price'].iloc[0]
        exit_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_exit_price'].iloc[0]
        
        pnl = (exit_price - entry_price) * lot_size - 60  # Assuming 60 for charges
        trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'profit_loss'] = pnl
        logging.info(f"P&L for {symbol}: {pnl}")

    except (FileNotFoundError, IndexError) as e:
        logging.error(f"Could not calculate P&L for {symbol}: {e}")
        trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'profit_loss'] = 'Calculation Error'

    logging.info(f"Trade log updated: \n{trade_log_df}")

    # Save trade log
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)
    output_filename = f"trade_log_{to_date_str}.csv"
    output_path = os.path.join(config.OUTPUT_DIR_TRADE_LOG, output_filename)
    
    trade_log_to_save = trade_log_df[trade_log_df['trade_entry_time'] == time_trade_taken]

    if os.path.exists(output_path):
        try:
            existing_df = pd.read_csv(output_path)
            combined_df = pd.concat([existing_df, trade_log_to_save], ignore_index=True)
            combined_df.drop_duplicates(subset=["Instrument_name", "trade_entry_time"], keep='last', inplace=True)
            combined_df.to_csv(output_path, index=False)
        except Exception as e:
            logging.error(f"Error appending to trade log: {e}")
    else:
        trade_log_to_save.to_csv(output_path, index=False)

    return True