2025-06-26 23:48:36 INFO: === ****************************************************** ===
2025-06-26 23:48:36 INFO: === ****************START LOGGING* (23:48:36) ************ ===
2025-06-26 23:48:36 INFO: === ****************************************************** ===
2025-06-26 23:48:36 INFO: === Enhanced Historical Fetcher with Option Data Integration ===
2025-06-26 23:48:36 INFO: Historical data output: processed_files
2025-06-26 23:48:36 INFO: Options data output: processed_options_files
2025-06-26 23:48:36 INFO: Options input file: User_options_input.csv
2025-06-26 23:48:36 INFO: ============================================================
2025-06-26 23:48:36 INFO: --- Processing Historical Data (23:48:36) ---
2025-06-26 23:48:36 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-26 23:48:36 INFO:  Fetching 1min data...
2025-06-26 23:49:47 INFO: === ****************************************************** ===
2025-06-26 23:49:47 INFO: === ****************START LOGGING* (23:49:47) ************ ===
2025-06-26 23:49:47 INFO: === ****************************************************** ===
2025-06-26 23:49:47 INFO: === Enhanced Historical Fetcher with Option Data Integration ===
2025-06-26 23:49:47 INFO: Historical data output: processed_files
2025-06-26 23:49:47 INFO: Options data output: processed_options_files
2025-06-26 23:49:47 INFO: Options input file: User_options_input.csv
2025-06-26 23:49:47 INFO: ============================================================
2025-06-26 23:49:47 INFO: --- Processing Historical Data (23:49:47) ---
2025-06-26 23:49:47 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-26 23:49:47 INFO:  Fetching 60min data...
2025-06-26 23:51:16 INFO: === ****************************************************** ===
2025-06-26 23:51:16 INFO: === ****************START LOGGING* (23:51:16) ************ ===
2025-06-26 23:51:16 INFO: === ****************************************************** ===
2025-06-26 23:51:16 INFO: === Enhanced Historical Fetcher with Option Data Integration ===
2025-06-26 23:51:16 INFO: Historical data output: processed_files
2025-06-26 23:51:16 INFO: Options data output: processed_options_files
2025-06-26 23:51:16 INFO: Options input file: User_options_input.csv
2025-06-26 23:51:16 INFO: ============================================================
2025-06-26 23:51:16 INFO: --- Processing Historical Data (23:51:16) ---
2025-06-26 23:51:16 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-26 23:51:16 INFO:  Fetching 1min data...
2025-06-26 23:54:56 INFO: === ****************************************************** ===
2025-06-26 23:54:56 INFO: === ****************START LOGGING* (23:54:56) ************ ===
2025-06-26 23:54:56 INFO: === ****************************************************** ===
2025-06-26 23:54:56 INFO: === Enhanced Historical Fetcher with Option Data Integration ===
2025-06-26 23:54:56 INFO: Historical data output: processed_files
2025-06-26 23:54:56 INFO: Options data output: processed_options_files
2025-06-26 23:54:56 INFO: Options input file: User_options_input.csv
2025-06-26 23:54:56 INFO: ============================================================
2025-06-26 23:54:56 INFO: --- Processing Historical Data (23:54:56) ---
2025-06-26 23:54:56 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-26 23:54:56 INFO:  Fetching 1min data...
2025-06-26 23:58:46 INFO: === ****************************************************** ===
2025-06-26 23:58:46 INFO: === ****************START LOGGING* (23:58:46) ************ ===
2025-06-26 23:58:46 INFO: === ****************************************************** ===
2025-06-26 23:58:46 INFO: === Enhanced Historical Fetcher with Option Data Integration ===
2025-06-26 23:58:46 INFO: Historical data output: processed_files
2025-06-26 23:58:46 INFO: Options data output: processed_options_files
2025-06-26 23:58:46 INFO: Options input file: User_options_input.csv
2025-06-26 23:58:46 INFO: ============================================================
2025-06-26 23:58:46 INFO: --- Processing Historical Data (23:58:46) ---
2025-06-26 23:58:46 INFO:  First run: scheduling all intervals for full historical fetch
2025-06-26 23:58:46 INFO:  Fetching 1min data...
