# NIFTY Options Algorithmic Trading Bot

This project is an algorithmic trading bot designed to trade NIFTY index options based on a custom strategy involving Weighted Moving Averages (WMA), RSI, ATR, and Supply/Demand zone analysis. The bot operates during Indian market hours, continuously fetching data, analyzing signals, and managing trades.

## Overview

The bot uses the Dhan HQ API to fetch historical and real-time market data for the NIFTY index and specific option contracts. It processes this data to generate trading signals and executes a state-driven logic to manage the entire lifecycle of a trade, from signal detection to exit.

## Features

- **Modular Architecture**: The code is separated into modules for configuration, data handling, technical processing, and trading logic, making it easy to maintain and extend.
- **State-Driven Trading**: Implements a robust state machine (`SCANNING`, `SIGNAL_FOUND`, `TRADE_READY`, `IN_TRADE`) to manage the trading process logically.
- **Multi-Timeframe Analysis**: Utilizes both 1-minute and 5-minute chart data for the NIFTY index to generate high-conviction trading signals.
- **Technical Indicator Suite**:
  - Weighted Moving Averages (WMA) for trend and crossover signals.
  - Relative Strength Index (RSI) and Average True Range (ATR) for momentum and volatility analysis.
  - Custom Supply and Demand zone detection and validation.
- **Dynamic Data Fetching**: Fetches full historical data on the first run and then polls for new data at a configurable interval.
- **Comprehensive Logging**: Logs all major actions, state changes, and trades to a daily log file for easy debugging and review.
- **Persistent Trade Records**: Saves a detailed log of every completed trade to a CSV file.

## Project Structure

```
Option_trade/
├── .venv/                     # Virtual environment
├── Dependencies/              # Caches the daily instrument master file
├── logs/                      # Stores daily log files
├── processed_files/           # Stores processed NIFTY index data files
├── processed_options_files/   # Stores processed options data files
├── trade_log_files/           # Stores CSV logs of all trades
├── main.py                    # Main entry point for the application
├── config.py                  # All configuration variables and API keys
├── data_handler.py            # Handles all data fetching and preparation
├── processing_functions.py    # Contains all technical analysis functions
├── trading_logic.py           # Contains the core trading strategy and rules
├── User_options_input.csv     # User-defined list of option contracts to track
├── Nse_Fno_Lot_Size.csv       # Lot sizes for F&O instruments
└── README.md                  # This file
```

## Setup and Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd Option_trade
    ```

2.  **Create and activate a Python virtual environment:**
    ```bash
    python -m venv .venv
    source .venv/bin/activate  # On Windows, use `.venv\Scripts\activate`
    ```

3.  **Install the required dependencies:**
    *(You may need to create a `requirements.txt` file)*
    ```bash
    pip install pandas pandas-ta dhanhq requests
    ```

## Configuration

Before running the bot, you need to configure it properly.

1.  **API Credentials**:
    Open `config.py` and replace the placeholder values for `TOKEN` and `CLIENT_CODE` with your actual Dhan API credentials.
    ```python
    # config.py
    TOKEN = "YOUR_DHAN_API_TOKEN"
    CLIENT_CODE = "YOUR_DHAN_CLIENT_ID"
    ```

2.  **Options to Trade**:
    Open `User_options_input.csv` and add the option contracts you want the bot to monitor and trade. The bot will select the appropriate contract (CALL or PUT) from this list based on the NIFTY signal.

    **Example `User_options_input.csv`:**
    ```csv
    UNDERLYING_SYMBOL,DISPLAY_NAME
    NIFTY,NIFTY 24 JUL 24000 CALL
    NIFTY,NIFTY 24 JUL 23800 PUT
    ```

3.  **Trading Parameters**:
    You can adjust parameters like `FETCH_INTERVAL_SECONDS` in `config.py` to change how frequently the bot polls for new data.

## How to Run

Once the setup and configuration are complete, you can start the trading bot by running `main.py`.

```bash
python main.py
```

The bot will start, print its configuration, and begin the data fetching and analysis loop. All activities will be printed to the console and logged in the `logs/` directory.

## Workflow

The bot operates based on a clear, state-driven workflow:

1.  **Initialization**: The bot starts and initializes its state to `SCANNING`.

2.  **State: `SCANNING`**:
    -   The bot fetches and processes 1-minute and 5-minute data for the NIFTY index.
    -   It calls `trading_logic.look_for_trade_signals()` to check for a synchronized `upward` (CALL) or `downward` (PUT) crossover signal on both timeframes.
    -   If a valid signal is found, it transitions to the `SIGNAL_FOUND` state.

3.  **State: `SIGNAL_FOUND`**:
    -   The bot fetches and processes the latest data for all option contracts listed in `User_options_input.csv`.
    -   It calls `trading_logic.get_info_for_trade()` to find a matching option contract (e.g., a PUT contract if the signal was `downward`).
    -   If a contract is found, it transitions to `TRADE_READY`. If not, it reverts to `SCANNING`.

4.  **State: `TRADE_READY`**:
    -   The bot continuously monitors the selected option's 1-minute chart.
    -   It calls `trading_logic.take_the_trade()` to check if the entry condition (e.g., price touching the 5-WMA) is met.
    -   If the entry condition is met, a trade is initiated (simulated in this version), and the state moves to `IN_TRADE`. If the signal is invalidated (e.g., by a reverse crossover), it reverts to `SCANNING`.

5.  **State: `IN_TRADE`**:
    -   The bot monitors the open position.
    -   `trading_logic.check_for_trade_exit()` is called to check for exit conditions:
        -   Profit target reached.
        -   Stop-loss triggered.
        -   Time-based exit (e.g., after 30 minutes).
        -   A reversal signal on the NIFTY 1-minute chart.
    -   When an exit condition is met, `trading_logic.exit_the_trade()` is called to log the P&L, and the state resets to `SCANNING` to look for the next opportunity.

## Disclaimer

This project is for educational and research purposes only. Algorithmic trading involves substantial risk and is not suitable for all investors. The author and contributors are not responsible for any financial losses incurred by using this software. Always backtest your strategies thoroughly and use them at your own risk.