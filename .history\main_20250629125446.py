# /home/<USER>/Algo-trade/Option_trade/main.py (formerly historical_fetcher.py)

import os
import datetime
import pandas as pd
import time
import logging

import config.config as config
import utils.data_handler as data_handler
import utils.trading_logic as trading_logic
from utils.processing_functions import process_dataframe

# === Logging Configuration ===
os.makedirs(config.LOG_DIR, exist_ok=True)
log_filename = os.path.join(config.LOG_DIR, f"Daily_log_{datetime.datetime.now().strftime('%Y-%m-%d')}.log")
logging.basicConfig(
    filename=log_filename,
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)



# Market open days: Monday (0) to Friday (4)
def is_market_open_day(date=None):
    if date is None:
        date = datetime.date.today()
    return date.weekday() < 5  # 0-4 are Monday-Friday

def main():
    """
    Main function to run the trading bot.
    """
    logging.info("=== ****************************************************** ===")
    logging.info(f"=== ****************START LOGGING* ({datetime.datetime.now().strftime('%H:%M:%S')}) ************ ===")
    logging.info("=== ****************************************************** ===")

    print("=== Algo Trading Bot Started ===")
    print(f"Output for processed NIFTY data: {config.OUTPUT_DIR}")
    print(f"Output for processed Options data: {config.OPTIONS_OUTPUT_DIR}")
    print(f"Options input file: {config.OPTIONS_CSV_FILE}")
    print("="*60)
    
    logging.info("=== Algo Trading Bot Started ===")
    logging.info(f"Output for processed NIFTY data: {config.OUTPUT_DIR}")
    logging.info(f"Output for processed Options data: {config.OPTIONS_OUTPUT_DIR}")
    logging.info(f"Options input file: {config.OPTIONS_CSV_FILE}")
    logging.info("="*60)

    # Initialize data stores and state variables
    first_run = True
    last_15_min_fetch_minute = -1
    
    # State management
    # Possible states: 'SCANNING', 'SIGNAL_FOUND', 'TRADE_READY', 'IN_TRADE'
    trade_state = 'SCANNING' 
    trade_info = {} # To store trade details
    master_dataframes = {}

    trade_log_columns = [
        "Instrument_name",
        "trade_entry_time",
        "trade_exit_time",
        "Trade_entry_price",
        "Trade_exit_price",
        "profit_loss"
    ]
    trade_log_df = pd.DataFrame(columns=trade_log_columns)

    # Create output directories if they don't exist
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    os.makedirs(config.OPTIONS_OUTPUT_DIR, exist_ok=True)
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)

    df_1, df_5 = None, None
    to_date_str = datetime.datetime.now().strftime('%Y-%m-%d')

    while True:
        now = datetime.datetime.now()
        current_time = now.time()
        # 
        # if current_time > config.MARKET_CLOSE:
        #     print(f"→ Market is closed (current time: {current_time}). Exiting...")
        #     logging.info(f"Market is closed (current time: {current_time}). Exiting...")
        #     break
        # if current_time < config.MARKET_START and current_time > config.MARKET_CLOSE:
        #     print(f"→ Market is closed (current time: {current_time}). Waiting...")
        #     logging.info(f"Market is closed (current time: {current_time}). Waiting...")
        #     time.sleep(60)  # Wait for 1 minute before checking again
        #     continue
        # Also capture the saturday and sunday market close and market holiday
        MARKET_IS_OPEN_TODAY = is_market_open_day(now.date())
        
            if current_time == config.DAY_END:
                print(f"→ Market is closed (current time: {current_time}). Exiting...")
                logging.info(f"Market is closed (current time: {current_time}). Exiting...")
                #clear the values in user input file and keep the columns name
                data_handler.clear_user_input_file()
                break
            # --- Historical NIFTY Data Processing ---
            print(f"\n--- Processing Historical NIFTY Data ({now.strftime('%H:%M:%S')}) ---")
            logging.info(f"--- Processing Historical NIFTY Data ({now.strftime('%H:%M:%S')}) ---")

            intervals_to_fetch = set()
            if first_run:
                print("→ First run: scheduling all intervals for full historical fetch")
                logging.info(" First run: scheduling all intervals for full historical fetch")
                intervals_to_fetch.update(["1", "5", "60", "daily"])
                first_run = False
            else:
                intervals_to_fetch.update(["1", "5"])

            if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
                print(f"→ Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
                logging.info(f" Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
                intervals_to_fetch.add("60")
                last_15_min_fetch_minute = now.minute

            for interval in intervals_to_fetch:
                print(f"  → Fetching NIFTY {interval}min data...")
                logging.info(f" Fetching NIFTY {interval}min data...")
                df, from_date_str, to_date_str = data_handler.fetch_historical_data(interval)

                if df is not None and not df.empty:
                    print(f"    ✓ Fetched {len(df)} new records")
                    logging.info(f" Fetched {len(df)} new records")
                    
                    df_processed = process_dataframe(df)

                    if interval == "1": df_1 = df_processed
                    elif interval == "5": df_5 = df_processed
                    
                    # Save processed file
                    try:
                        interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                        output_filename = f"NIFTY_INDEX_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                        output_path = os.path.join(config.OUTPUT_DIR, output_filename)
                        df_processed.to_csv(output_path, index=False)
                        print(f"    ✓ Saved processed file: {output_filename}")
                        logging.info(f" Saved processed file: {output_filename}")
                    except Exception as e:
                        print(f"    ✗ Error saving processed data for interval {interval}: {str(e)}")
                        logging.error(f" Error saving processed data for interval {interval}: {str(e)}")
                else:
                    print(f"    ✗ No data available for NIFTY {interval}min interval")
                    logging.error(f" No data available for NIFTY {interval}min interval")
            
            # --- Trading Logic State Machine ---

            if trade_state == 'SCANNING':
                logging.info("State: SCANNING for trade signals.")
                option_type, signal_found = trading_logic.look_for_trade_signals(df_1, df_5)
                if signal_found == 'True':
                    trade_info["option_type"] = option_type
                    trade_state = 'SIGNAL_FOUND'
                    logging.info(f"State change: SCANNING -> SIGNAL_FOUND. Signal for {option_type}.")
            
            if trade_state in ['SIGNAL_FOUND', 'TRADE_READY', 'IN_TRADE']:
                print("\n--- Processing Option Data ---")
                logging.info("--- Processing Option Data ---")
                try:
                    options_results = data_handler.process_options_csv()
                    if options_results["success"]:
                        master_dataframes = options_results.get('dataframes', {})
                        logging.info(f"Options processing completed: {options_results['successful_fetches']} successful, {options_results['failed_fetches']} failed")
                    else:
                        logging.error(f"Options processing failed: {options_results.get('error', 'Unknown error')}")
                        trade_state = 'SCANNING' # Reset on failure
                except Exception as e:
                    logging.error(f"Error during options processing: {str(e)}")
                    trade_state = 'SCANNING' # Reset on failure

            if trade_state == 'SIGNAL_FOUND':
                logging.info("State: SIGNAL_FOUND. Getting trade info.")
                trade_symbol, entry_candle = trading_logic.get_info_for_trade(master_dataframes, trade_info["option_type"])
                if trade_symbol is not None and entry_candle is not None:
                    trade_info['symbol'] = trade_symbol
                    trade_info['entry_candle'] = entry_candle
                    trade_state = 'TRADE_READY'
                    logging.info(f"State change: SIGNAL_FOUND -> TRADE_READY for {trade_symbol}.")
                else:
                    logging.warning(f"Could not find a tradable option for {trade_info['option_type']}. Resetting.")
                    trade_state = 'SCANNING'

            elif trade_state == 'TRADE_READY':
                logging.info(f"State: TRADE_READY. Attempting to take trade for {trade_info['symbol']}.")
                entry_time, trade_status = trading_logic.take_the_trade(
                    trade_info['symbol'], trade_info['entry_candle'], master_dataframes
                )
                if trade_status == "Trade Taken":
                    trade_info['entry_time'] = entry_time
                    trade_info['entry_price'] = master_dataframes[trade_info['symbol']]['df_opt_1min'].iloc[-1]['low']
                    
                    new_row = {"Instrument_name": trade_info['symbol'], "trade_entry_time": trade_info['entry_time'], "Trade_entry_price": trade_info['entry_price']}
                    trade_log_df = pd.concat([trade_log_df, pd.DataFrame([new_row])], ignore_index=True)
                    
                    trade_state = 'IN_TRADE'
                    logging.info(f"State change: TRADE_READY -> IN_TRADE. Trade taken for {trade_info['symbol']} at {trade_info['entry_price']}.")
                elif trade_status == "False":
                    logging.info(f"Trade invalidated for {trade_info['symbol']}. Resetting.")
                    trade_state = 'SCANNING'

            elif trade_state == 'IN_TRADE':
                logging.info(f"State: IN_TRADE. Monitoring trade for {trade_info['symbol']}.")
                exit_status = trading_logic.check_for_trade_exit(
                    df_1, trade_info['symbol'], trade_info['entry_time'], master_dataframes, trade_log_df
                )
                if exit_status == 'Trade Exit':
                    logging.info(f"Exit signal received for {trade_info['symbol']}. Exiting trade.")
                    trading_logic.exit_the_trade(
                        trade_info['symbol'], trade_info['entry_time'], master_dataframes, trade_log_df, to_date_str
                    )
                    trade_state = 'SCANNING'
                    trade_info = {} # Reset trade info
                    logging.info("State change: IN_TRADE -> SCANNING. Trade exited.")

            print(f"\n{'='*60}")
            print(f"Current State: {trade_state}. Next update in {config.FETCH_INTERVAL_SECONDS} seconds...")
            print(f"{'='*60}\n")
            logging.info(f"{'='*60}")
            logging.info(f"Current State: {trade_state}. Next update in {config.FETCH_INTERVAL_SECONDS} seconds...")
            logging.info(f"{'='*60}\n")

            time.sleep(config.FETCH_INTERVAL_SECONDS)

if __name__ == "__main__":
    main()
