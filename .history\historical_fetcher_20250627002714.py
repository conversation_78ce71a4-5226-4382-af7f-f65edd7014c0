# historical_fetcher_v2.py
# Enhanced version with integrated option data fetching capabilities
# Fetch past 5 trading days of intraday data using DhanHQ 2.0 compatible API (with from_date/to_date)
# Added option data fetching for multiple timeframes from User_options_input.csv

import os
import datetime
import pandas as pd
import requests
import time
from dhanhq import dhanhq
import logging
import warnings
warnings.filterwarnings("ignore")
# Optional import for Tradehull
# try:
#     from Dhan_Tradehull import Tradehull
#     TRADEHULL_AVAILABLE = True
# except ImportError:
#     Tradehull = None
#     TRADEHULL_AVAILABLE = False

from processing_functions import process_dataframe

# === Logging Configuration ===
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_filename = os.path.join(log_dir, f"Daily_log_{datetime.datetime.now().strftime('%Y-%m-%d')}.log")
logging.basicConfig(
    filename=log_filename,
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# === Configuration ===
token = "eeyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUzNTU1MTc1LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.ust0dkFNo13otgHcamTyDPeYodL4BwVVkX6DHNWroXhH5xP-YHQYfzgx25ii_uaK7rfirpv9zUE4LTf4JFgyhA"
client_code = "1105577608"

# Output directories
output_dir = "processed_files"
options_output_dir = "processed_options_files"
output_dir_trade_log = "trade_log_files"

# Option data configuration
options_csv_file = "User_options_input.csv"
options_lot_size_csv = "Nse_Fno_Lot_Size.csv"
option_timeframes = ["1", "5"]  # 1min/10days, 5min/20days, 60min/60days

# Global variable for instrument data
instrument_df = None


# === Option Data Functions ===

def get_instrument_file():
    """
    Get instrument file from Dhan API or local cache.
    Follows the same pattern as Option_fetch.py but adapted for historical_fetcher.py structure.

    Returns:
        pd.DataFrame: Instrument data DataFrame
    """
    global instrument_df

    try:
        current_date = time.strftime("%Y-%m-%d")
        expected_file = f'all_instrument {current_date}.csv'
        dependencies_dir = "Dependencies"

        # Create Dependencies directory if it doesn't exist
        os.makedirs(dependencies_dir, exist_ok=True)

        # Clean up old instrument files
        if os.path.exists(dependencies_dir):
            for item in os.listdir(dependencies_dir):
                if item.startswith('all_instrument') and current_date not in item:
                    file_path = os.path.join(dependencies_dir, item)
                    if os.path.isfile(file_path):
                        try:
                            os.remove(file_path)
                            #print(f"  ✓ Removed old instrument file: {item}")
                            logging.info(f"  Removed old instrument file: {item}")
                        except Exception as e:
                            print(f"  ✗ Error removing old file {item}: {str(e)}")

        # Check if current date file exists
        expected_file_path = os.path.join(dependencies_dir, expected_file)
        if os.path.exists(expected_file_path):
            try:
                #print(f"  → Reading existing instrument file: {expected_file}")
                logging.info(f"  Reading existing instrument file: {expected_file}")
                instrument_df = pd.read_csv(expected_file_path, low_memory=False)
                return instrument_df
            except Exception as e:
                print(f"  ✗ Error reading existing instrument file: {str(e)}")
                logging.error(f"  ✗ Error reading existing instrument file: {str(e)}")
                # Fall through to download new file

        # Download new instrument file from Dhan
        #print("  → Downloading new instrument file from Dhan API")
        #logging.info("  → Downloading new instrument file from Dhan API")
        instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

        # Save the downloaded file for future use
        try:
            instrument_df.to_csv(expected_file_path, index=False)
            #print(f"  ✓ Saved instrument file: {expected_file}")
            #logging.info(f"  ✓ Saved instrument file: {expected_file}")
        except Exception as e:
            print(f"  ✗ Error saving instrument file: {str(e)}")
            logging.error(f"  ✗ Error saving instrument file: {str(e)}")

        return instrument_df

    except Exception as e:
        print(f"  ✗ Error in get_instrument_file: {str(e)}")
        logging.error(f"  ✗ Error in get_instrument_file: {str(e)}")
        return None


def get_option_security_details(tradingsymbol, exchange):
    """
    Get security details for option symbols from instrument data.

    Args:
        tradingsymbol (str): Trading symbol (e.g., "NIFTY 26 JUN 24500 PUT")
        exchange (str): Exchange (e.g., "NFO")

    Returns:
        tuple: (security_id, instrument_type) or (None, None) if not found
    """
    global instrument_df

    try:
        if instrument_df is None:
            instrument_df = get_instrument_file()

        if instrument_df is None:
            logging.error(f"  ✗ Instrument data not available for {tradingsymbol}")
            return None, None

        # Exchange mapping
        instrument_exchange = {
            'NSE': 'NSE', 'BSE': 'BSE', 'NFO': 'NSE',
            'BFO': 'BSE', 'MCX': 'MCX', 'CUR': 'NSE'
        }

        # Index exchange mapping
        index_exchange = {
            "NIFTY": 'NSE', "BANKNIFTY": "NSE", "FINNIFTY": "NSE",
            "MIDCPNIFTY": "NSE", "BANKEX": "BSE", "SENSEX": "BSE"
        }

        # Adjust exchange for index symbols
        if tradingsymbol in index_exchange:
            exchange = index_exchange[tradingsymbol]

        # Filter instrument data
        filtered_df = instrument_df[
            ((instrument_df['SEM_TRADING_SYMBOL'] == tradingsymbol) |
             (instrument_df['SEM_CUSTOM_SYMBOL'] == tradingsymbol)) &
            (instrument_df['SEM_EXM_EXCH_ID'] == instrument_exchange[exchange])
        ]

        if filtered_df.empty:
            logging.error(f"  ✗ No instrument found for {tradingsymbol} on {exchange}")
            return None, None

        # Get the last matching record
        last_record = filtered_df.iloc[-1]
        security_id = last_record['SEM_SMST_SECURITY_ID']
        instrument_type = last_record['SEM_INSTRUMENT_NAME']

        #print(f"  ✓ Found {tradingsymbol}: security_id={security_id}, instrument_type={instrument_type}")
        return security_id, instrument_type
    except Exception as e:
        logging.error(f"  ✗ Error getting security details for {tradingsymbol}: {str(e)}")
        return None, None


def fetch_historical_data(interval="60"):
    """
    Fetch historical data from Dhan API based on interval
    Intervals:
    - "1": 1 minute (5 days)
    - "5": 5 minutes (10 days)
    - "60": 60 minutes (30 days)
    - "daily": Daily data (1 year)
    """
    security_id = "13"  # NIFTY
    exchange_segment = "IDX_I"
    instrument_type = "INDEX"
    
    now = datetime.datetime.now()
    today = now.date()
    current_time = now.time()
    market_start = datetime.time(9, 15)
    today_curr_date_time = f"{today} {current_time}"
    today_market_start = f"{today} {market_start}"

    # print("today_curr_date_time: ",today_curr_date_time)
    # print("today_market_start: ",today_market_start)
    
    if today_curr_date_time < today_market_start:
        end_date = today - datetime.timedelta(days=1)
        while end_date.weekday() > 4:  # 5 is Saturday, 6 is Sunday
            end_date = end_date - datetime.timedelta(days=1)
    else:
        end_date = today
    
    # print("End date: ",end_date)

    # Calculate from_date based on interval
    if interval == "1":
        from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "5":
        from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "60":
        from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "daily":
        from_date = (end_date - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    # For intraday data, always use 15:29 as the end time
    to_date = end_date.strftime("%Y-%m-%d 15:29:00") if interval != "daily" else end_date.strftime("%Y-%m-%d")

    #   print("To date validate: ",to_date)

    # === Request Setup ===
    headers = {
        "access-token": token,
        "Content-Type": "application/json"
    }

    if interval == "daily":
        # Initialize DhanHQ client for daily data
        client = dhanhq("1105577608", token)
        res = client.historical_daily_data(
            security_id=security_id,
            exchange_segment=exchange_segment,
            instrument_type=instrument_type,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
        data = res
    else:
        # Setup for intraday data
        payload = {
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "instrument": instrument_type,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()

    # #print the response to understand the structure
    #print("API Response:")
    # #print(data)
    #print("\nResponse keys:", list(data.keys()) if isinstance(data, dict) else "Not a dictionary")

    # Check if the response is successful and has the expected structure
    if isinstance(data, dict):
        if "data" in data and interval != "daily":
            chart_data = data["data"]
            #print("\nChart data keys:", list(chart_data.keys()) if isinstance(chart_data, dict) else "Chart data is not a dictionary")
            
            # Create DataFrame from individual lists
            df = pd.DataFrame({
                "open": chart_data["open"],
                "high": chart_data["high"],
                "low": chart_data["low"],
                "close": chart_data["close"],
                "volume": chart_data["volume"],
                "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
            })
        elif "data" in data and interval == "daily":
            # Handle daily data format
            df = pd.DataFrame(data["data"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
        elif all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
            # Data is directly in the response
            df = pd.DataFrame({
                "open": data["open"],
                "high": data["high"],
                "low": data["low"],
                "close": data["close"],
                "volume": data["volume"],
                "timestamp": pd.to_datetime(data["timestamp"], unit="s", utc=True)
            })
        else:
            #print("❌ Unexpected response structure. Cannot create DataFrame.")
            return None

    # Convert timestamp to IST (UTC+5:30)
    IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    # Convert to IST and overwrite 'timestamp'
    df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

    # Filter out data points after 15:29 for intraday data
    if interval != "daily":
        df = df[df["timestamp"].dt.time <= datetime.time(15, 29)]

    # Reorder columns: IST timestamp first
    df = df[["timestamp", "open", "high", "low", "close", "volume"]]

    # Create filename based on interval
    # interval_suffix = "daily" if interval == "daily" else f"{interval}min"
    # filename = f"NIFTY_INDEX_{interval_suffix}_{from_date[:10]}_to_{to_date[:10]}.csv"
    
    # # Save to CSV
    # output_path = os.path.join(output_dir, filename)
    # df.to_csv(output_path, index=False)
    # df.to_csv(filename, index=False)
    
    # Output preview
    #print("✅ Data fetched from {} to {}".format(from_date, to_date))
    # print(df.head(10))
    # print(df.tail(10))
    
    logging.info(f" Data fetched from {from_date} to {to_date}")
    return df, from_date[:10], to_date[:10]


def fetch_option_data(security_id, exchange_segment, instrument_type, interval, tradingsymbol):
    """
    Fetch option data from Dhan API for a specific security and interval.
    Follows the same patterns as fetch_historical_data but for options.

    Args:
        security_id (str): Security ID from instrument data
        exchange_segment (str): Exchange segment (e.g., Dhan.FNO)
        instrument_type (str): Instrument type from instrument data
        interval (str): Time interval ("1", "5", "60")
        tradingsymbol (str): Trading symbol for logging/naming

    Returns:
        tuple: (DataFrame, from_date_str, to_date_str) or (None, None, None) if error
    """
    try:
        # Calculate date ranges based on interval (following the requirements)
        now = datetime.datetime.now()
        today = now.date()
        current_time = now.time()
        market_start = datetime.time(9, 15)
        today_curr_date_time = f"{today} {current_time}"
        today_market_start = f"{today} {market_start}"

        # Determine end date
        if today_curr_date_time < today_market_start:
            end_date = today - datetime.timedelta(days=1)
            while end_date.weekday() > 4:  # Skip weekends
                end_date = end_date - datetime.timedelta(days=1)
        else:
            end_date = today

        # Calculate from_date based on interval requirements
        if interval == "1":
            # 1-minute intervals for 10 days
            from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d")
        elif interval == "5":
            # 5-minute intervals for 20 days
            from_date = (end_date - datetime.timedelta(days=20)).strftime("%Y-%m-%d")
        elif interval == "60":
            # 60-minute intervals for 60 days
            from_date = (end_date - datetime.timedelta(days=60)).strftime("%Y-%m-%d")
        else:
            raise ValueError(f"Invalid interval for options: {interval}")

        to_date = end_date.strftime("%Y-%m-%d")

        #print(f"  → Fetching option data for {tradingsymbol}: {from_date} to {to_date}, interval: {interval}")

        # Initialize Dhan client
        client = dhanhq(client_code, token)

        # Fetch option data using intraday_minute_data
        response = client.intraday_minute_data(
            str(security_id),
            exchange_segment,
            instrument_type,
            from_date,
            to_date,
            int(interval)
        )

        if not response or 'data' not in response:
            logging.error(f"  ✗ No data received for {tradingsymbol}")
            return None, None, None

        # Create DataFrame from response
        df = pd.DataFrame(response['data'])

        if df.empty:
            logging.error(f"  ✗ Empty data received for {tradingsymbol}")
            return None, None, None

        # Convert timestamp using Dhan's convert_to_date_time method
        df['timestamp'] = df['timestamp'].apply(lambda x: client.convert_to_date_time(x))

        # Convert to pandas datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Ensure IST timezone
        if df['timestamp'].dt.tz is None:
            # Assume the data is already in IST
            IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
            df['timestamp'] = df['timestamp'].dt.tz_localize(IST)

        # Filter out data points after 15:29 for intraday data
        df = df[df['timestamp'].dt.time <= datetime.time(15, 29)]

        # Reorder columns to match historical data format
        expected_columns = ["timestamp", "open", "high", "low", "close", "volume"]
        available_columns = [col for col in expected_columns if col in df.columns]
        df = df[available_columns]
        # print("Option name: ",tradingsymbol, "Interval: ",interval)
        # print(df.head(5))
        # print(df.tail(5))
        #print(f"  ✓ Fetched {len(df)} records for {tradingsymbol}")
        return df, from_date, to_date

    except Exception as e:
        logging.error(f"  ✗ Error fetching option data for {tradingsymbol}: {str(e)}")
        return None, None, None


def process_options_csv():
    """
    Process all entries in User_options_input.csv and fetch option data for all timeframes.
    Follows the established patterns for data processing and file management.

    Returns:
        dict: Dictionary with results summary


        # Process options and get enhanced results
            results = process_options_csv()
            dataframes = results["dataframes"]

            # Access specific data
            put_data = dataframes["NIFTY 26 JUN 24500 PUT"]["df_opt_1min"]
            latest_price = put_data['close'].iloc[-1]
            latest_signal = put_data['crossover_signal'].iloc[-1]

            # Manage trade status
            dataframes["NIFTY 26 JUN 24500 PUT"]["Trade_Taken"] = True

    """
    try:
        # Check if options CSV file exists
        if not os.path.exists(options_csv_file):
            logging.error(f"  ✗ Options CSV file not found: {options_csv_file}")
            return {"success": False, "error": "CSV file not found"}

        # Read options CSV
        #print(f"  → Reading options input file: {options_csv_file}")
        user_df = pd.read_csv(options_csv_file)

        if user_df.empty:
            logging.error(f"  ✗ Options CSV file is empty")
            return {"success": False, "error": "CSV file is empty"}

        # Create options output directory
        os.makedirs(options_output_dir, exist_ok=True)

        # Initialize instrument data
        global instrument_df
        if instrument_df is None:
            instrument_df = get_instrument_file()

        # Exchange mappings (from Option_fetch.py)
        script_exchange = {
            "NSE": "NSE", "NFO": "FNO", "BFO": "BSE_FNO",
            "CUR": "CUR", "BSE": "BSE", "MCX": "MCX", "INDEX": "INDEX"
        }

        results = {
            "success": True,
            "total_entries": len(user_df),
            "processed_entries": 0,
            "successful_fetches": 0,
            "failed_fetches": 0,
            "details": []
        }

        # Initialize master DataFrame dictionary for in-memory storage
        master_dataframes = {}

        # Process each entry in the CSV
        for index, row in user_df.iterrows():
            try:
                underlying_symbol = row['UNDERLYING_SYMBOL']
                display_name = row['DISPLAY_NAME']

                #print(f"\n  → Processing entry {index + 1}/{len(user_df)}: {display_name}")

                # Initialize nested dictionary for this symbol
                if display_name not in master_dataframes:
                    # master_dataframes[display_name] = {"Trade_Taken": False}
                    master_dataframes[display_name] = {"Option_symbol": display_name}    

                # Determine exchange (default to NFO for options)
                exchange = 'NFO'

                # Get security details
                security_id, instrument_type = get_option_security_details(display_name, exchange)

                if security_id is None or instrument_type is None:
                    logging.error(f"  ✗ Could not find security details for {display_name}")
                    results["failed_fetches"] += 1
                    results["details"].append({
                        "symbol": display_name,
                        "status": "failed",
                        "error": "Security details not found"
                    })
                    continue

                # Get exchange segment for API call
                client = dhanhq(client_code, token)
                exchange_segment = client.FNO  # Use FNO for options

                entry_results = {"symbol": display_name, "timeframes": {}}

                # Fetch data for each timeframe
                for interval in option_timeframes:
                    try:
                        df, from_date, to_date = fetch_option_data(
                            security_id, exchange_segment, instrument_type, interval, display_name
                        )                        
                         
                        #df_interval,display_name

                        if df is not None and not df.empty:
                            # Process the dataframe using existing processing functions
                            df_processed = process_dataframe(df)

                            # Store processed DataFrame in master dictionary
                            master_dataframes[display_name][f"df_opt_{interval}min"] = df_processed

                            # Create filename following existing patterns
                            clean_symbol = display_name.replace(" ", "_").replace("/", "_")
                            interval_suffix = f"{interval}min"
                            output_filename = f"{clean_symbol}_{interval_suffix}_{from_date}_to_{to_date}_processed.csv"
                            output_path = os.path.join(options_output_dir, output_filename)

                            # Save processed data
                            df_processed.to_csv(output_path, index=False)
                            logging.info(f"     Saved {interval}min data: {output_filename}")

                            entry_results["timeframes"][interval] = {
                                "status": "success",
                                "records": len(df_processed),
                                "file": output_filename
                            }
                            results["successful_fetches"] += 1
                        else:
                            logging.error(f"    ✗ No data for {interval}min timeframe")
                            entry_results["timeframes"][interval] = {
                                "status": "failed",
                                "error": "No data received"
                            }
                            results["failed_fetches"] += 1

                    except Exception as e:
                        logging.error(f"    ✗ Error processing {interval}min timeframe: {str(e)}")
                        entry_results["timeframes"][interval] = {
                            "status": "failed",
                            "error": str(e)
                        }
                        results["failed_fetches"] += 1

                entry_results["status"] = "processed"
                results["details"].append(entry_results)
                results["processed_entries"] += 1

            except Exception as e:
                logging.error(f"  ✗ Error processing entry {index + 1}: {str(e)}")
                results["failed_fetches"] += 1
                results["details"].append({
                    "symbol": row.get('DISPLAY_NAME', f'Entry {index + 1}'),
                    "status": "failed",
                    "error": str(e)
                })

        #print(f"\n  ✓ Options processing complete: {results['successful_fetches']} successful, {results['failed_fetches']} failed")
        # for symbol in master_dataframes:
        #     print("Symbol_option type:\n",symbol.split()[-1])
        #     print("\nSymbol: ", symbol)
        #     print("master dataframes:\n",master_dataframes[symbol]['df_opt_1min'].head(2))

        # print("master dataframes:\n", master_dataframes['dataframes'])

        # Return enhanced results with master dataframes
        return {
            "success": True,
            "total_entries": len(user_df),
            "processed_entries": results["processed_entries"],
            "successful_fetches": results["successful_fetches"],
            "failed_fetches": results["failed_fetches"],
            "details": results["details"],
            "dataframes": master_dataframes  # New addition for in-memory access
        }

    except Exception as e:
        logging.error(f"  ✗ Error in process_options_csv: {str(e)}")
        return {"success": False, "error": str(e)}

def find_latest_signals(df):
    """
    Analyze a processed option DataFrame to identify the most recent trading signals.

    1. Finds the most recent crossover signal ('upward' or 'downward') in the 'crossover_signal' column.
    2. Finds the most recent supply/demand zone ('Supply' or 'Demand') in the 'Zones_sup_dem' column.

    Args:
        df (pd.DataFrame): Processed DataFrame containing at least the columns:
            - 'timestamp'
            - 'crossover_signal'
            - 'Zones_sup_dem'
            - 'Zone_Status'

    Returns:
        dict: {
            'latest_crossover': {
                'timestamp': ...,
                'signal': ...,
                'row_index': ...
            } or None,
            'latest_zone': {
                'timestamp': ...,
                'zone_type': ...,
                'zone_status': ...,
                'row_index': ...
            } or None
        }
        If no signal/zone is found, the corresponding value is None.
    """
    result = {
        'latest_crossover': None,
        'latest_zone': None
    }

    # Check for empty DataFrame
    if df is None or df.empty:
        return result

    # Check for required columns
    required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'WMA5', 'WMA10', 'WMA45',	'WMA65', 'WMA90', 'RSI_14', 'ATR_14',
'crossover_signal', 'Zones_sup_dem', 'Zone_Status']
    for col in required_cols:
        if col not in df.columns:
            # Return early if any required column is missing
            return result

    # --- Find most recent crossover signal ---
    try:
        mask_crossover = df['crossover_signal'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            # result['latest_crossover'] = {
            #     'timestamp': df.at[last_idx, 'timestamp'],
            #     'signal': df.at[last_idx, 'crossover_signal'],
            #     'row_index': last_idx
            # }
            result['latest_crossover'] = df.iloc[last_idx][:]
    except Exception as e:
        # If any error, leave as None
        pass

    # --- Find most recent supply/demand zone ---
    try:
        mask_zone = (df['Zones_sup_dem'].isin(['Supply', 'Demand']) &
                     df['Zones_sup_dem'].notna() &
                    (df['Zones_sup_dem'].astype(str).str.strip() != '') &
                     df['Zone_Status'].isin(['Valid', 'Tested']))
        if mask_zone.any():
            last_idx = df[mask_zone].index[-1]
            # result['latest_zone'] = {
            #     'timestamp': df.at[last_idx, 'timestamp'],
            #     'zone_type': df.at[last_idx, 'Zones_sup_dem'],
            #     'zone_status': df.at[last_idx, 'Zone_Status'],
            #     'row_index': last_idx
            # }
            result['latest_zone'] = df.iloc[last_idx][:]
    except Exception as e:
        # If any error, leave as None
        pass
    # print("Exiting the new function find_latest_signals", result)
    return result

def look_for_trade_signals(df_1, df_5):
    logging.info("Entering function: look_for_trade_signals")
    latest_1min = find_latest_signals(df_1) #crossover and zone details
    latest_5min = find_latest_signals(df_5) #crossover and zone details
        
    last_stored_1min_data = df_1.iloc[-1][:] # Most recent candle details
    last_stored_5min_data = df_5.iloc[-1][:] # Most recent candle details
    
    #if(latest_1min['timestamp'] != last_stored_1min_data['timestamp']):
    if (latest_1min['latest_crossover'] is not None and
        latest_1min['latest_crossover']['timestamp'] != last_stored_1min_data['timestamp']):
        if (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) > datetime.timedelta(minutes=3): 
            logging.info(f"crossover happened long back. No Trade {latest_1min['latest_crossover']['timestamp']}")
            return "None", 'False'
        else:
            logging.info(f"Inside look_for_trade_signals {latest_1min['latest_crossover']['crossover_signal']}")
            if(latest_1min['latest_crossover'] is not None) & (latest_5min['latest_crossover'] is not None):
                logging.info(f"latest crossover: {latest_1min['latest_crossover']['crossover_signal']}")
                logging.info(f"latest zone: {latest_1min['latest_zone']['Zones_sup_dem']}")
            
                #if(latest_5min['latest_crossover']['crossover_signal'] == 'downward'):
                if((latest_5min['latest_crossover']['crossover_signal'] == 'downward') and
                   (latest_1min['latest_crossover']['crossover_signal'] == 'downward')):
                    if (last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA10"] and 
                        last_stored_5min_data["high"] < last_stored_5min_data["WMA10"]):
                        logging.info("Need to Buy Puts")
                        logging.info(f"last stored 1 min candle details: \n {last_stored_1min_data}")
                        logging.info(f"1 min latest crossover: \n {latest_1min['latest_crossover']}")
                        logging.info(f"last stored 5 min candle details: \n {last_stored_5min_data}")
                        logging.info(f"5 min latest crossover: \n {latest_5min['latest_crossover']}")
                        return "PUT", 'True'

                #elif(latest_5min['latest_crossover']['crossover_signal'] == 'upward'):
                elif ((latest_5min['latest_crossover']['crossover_signal'] == 'upward') and
                      (latest_1min['latest_crossover']['crossover_signal'] == 'upward')):
                        if (last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA10"] and 
                            last_stored_5min_data["low"] > last_stored_5min_data["WMA10"]):
                            logging.info("Need to Buy Calls")
                            logging.info(f"last stored 1 min candle details: \n {last_stored_1min_data}")
                            logging.info(f"1 min latest crossover: \n {latest_1min['latest_crossover']}")
                            logging.info(f"last stored 5 min candle details: \n {last_stored_5min_data}")
                            logging.info(f"5 min latest crossover: \n {latest_5min['latest_crossover']}")
                            return "CALL", 'True'
            else:
                logging.info("Exiting function (3): look_for_trade_signals")
                logging.info(f"No latest crossover ??? {latest_1min['latest_crossover']}")
                return "None", 'False'
    else:
        logging.info("Exiting function (1): look_for_trade_signals")
        return "None", 'False'
    
    logging.info("Exiting function (2): look_for_trade_signals")
    return "None", 'False'


def Get_info_for_trade(master_dataframes, Option_type_to_trade):
    logging.info("Entering function: Get_info_for_trade")
    Trade_symbol = "None"
    for symbol in master_dataframes:
        if symbol.split()[-1] == Option_type_to_trade:
            Trade_symbol = symbol
            latest_1min_option_candle = master_dataframes[Trade_symbol]['df_opt_1min'].iloc[-1][:]
    logging.info("Exiting function: Get_info_for_trade")
    return Trade_symbol, latest_1min_option_candle


def take_the_trade(symbol, Option_1min_ready_for_trade_candle, master_dataframes):
    logging.info("Entering function: take_the_trade")
    latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1][:]
    if symbol.split()[-1] == "PUT":
        if(latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']):   # Need to fine tune this
            logging.info(f"{symbol} Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['low']}")
            logging.info(f"candle info: {latest_1min_option_candle}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
        if(latest_1min_option_candle['crossover_signal'] == 'upward' and
           latest_1min_option_candle['timestamp'] > Option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"{symbol} Can't take Trade. Not valid for trade anymore {latest_1min_option_candle['timestamp']}")
            return latest_1min_option_candle['timestamp'], "False"
        
    elif symbol.split()[-1] == "CALL":
        if(latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']): # Need to fine tune this
            logging.info(f"{symbol} Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['low']}")
            logging.info(f"candle info: {latest_1min_option_candle}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
        if(latest_1min_option_candle['crossover_signal'] == 'downward' and
           latest_1min_option_candle['timestamp'] > Option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"{symbol} Can't take Trade. Not valid for trade anymore {latest_1min_option_candle['timestamp']}")
            return latest_1min_option_candle['timestamp'], "False"
        
    else:
        logging.info(f"No trade taken. Invalid symbol {symbol}")
        return latest_1min_option_candle['timestamp'], "False"
    # If none of the above conditions are met, return a default tuple
    logging.info("Exiting function: take_the_trade")
    return None, "Take trade"


def check_for_trade_exit(df_1, df_5,Trade_symbol, Time_trade_taken, master_dataframes, trade_log_df):
    #When in Puts trade, upward crossover is exit
    #When in Calls trade, downward crossover is exit
    logging.info("Entering function: check_for_trade_exit")
    latest_1min_option_candle = master_dataframes[Trade_symbol]['df_opt_1min'].iloc[-1][:]
    latest_5min_option_candle = master_dataframes[Trade_symbol]['df_opt_5min'].iloc[-1][:]
    result_1min = find_latest_signals(df_1)
    result_5min = find_latest_signals(df_5)
    trade_log_recent_entry = trade_log_df.iloc[-1][:]

    if((latest_1min_option_candle['low'] > (trade_log_recent_entry['Trade_entry_price'] + 15)) and 
        (latest_1min_option_candle['low'] <= (trade_log_recent_entry['Trade_entry_price'] +30))):
        logging.info(f"Exit the Trade {Trade_symbol} for good profit \n {latest_1min_option_candle}")
        return 'Trade Exit'
    
    if (latest_1min_option_candle['low'] <= (latest_5min_option_candle['WMA10'] - 0.10)):
        logging.info(f"Exit the Trade {Trade_symbol} to cut the losses \n {latest_1min_option_candle} and \n {latest_5min_option_candle}")
        return 'Trade Exit'
    
    if(latest_1min_option_candle['timestamp'] - Time_trade_taken) > datetime.timedelta(minutes=30):
        logging.info(f"Exit the Trade {Trade_symbol}")
        return 'Trade Exit'

    if Trade_symbol.split()[-1] == "PUT":
        if(result_1min['latest_crossover']['crossover_signal'] == 'upward' and
           result_1min['latest_crossover']['timestamp'] > Time_trade_taken):
            logging.info("Exit Puts Trade")
            return 'Trade Exit'
        else:
            logging.info(f"Continue to be on trade. Not exiting yet. {Trade_symbol}")
            return 'Trade Taken'
        #return tradesymbol,true
    #Exit Puts

    elif Trade_symbol.split()[-1] == "CALL":
        if(result_1min['latest_crossover']['crossover_signal'] == 'downward' and
           result_1min['latest_crossover']['timestamp'] > Time_trade_taken):
            logging.info("Exit Calls Trade")
            return 'Trade Exit'
        else:
            logging.info(f"Continue to be on trade. Not exiting yet. {Trade_symbol}")
            return 'Trade Taken'
        #return tradesymbol,true
    #Exit Calls
    else:
        logging.info("Exiting function: check_for_trade_exit")
        return 'Trade Taken'  

def exit_the_trade(symbol, Option_1min_ready_for_trade_candle, master_dataframes, trade_log_df):
    logging.info("Entering function: exit_the_trade")
    latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1][:]
    logging.info(f"{symbol} Trade Exited at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['close']}")
    logging.info("Exiting function: exit_the_trade")
    
    trade_log_df.loc[trade_log_df['trade_entry_time'] == Time_trade_taken, 'trade_exit_time'] = master_dataframes[symbol]['df_opt_1min'].iloc[-1]['timestamp']
    trade_log_df.loc[trade_log_df['trade_entry_time'] == Time_trade_taken, 'Trade_exit_price'] = master_dataframes[symbol]['df_opt_1min'].iloc[-1]['close']
    user_df = pd.read_csv(options_csv_file)
    for(index, row) in user_df.iterrows():
        if (row['DISPLAY_NAME'] == symbol):
            name_of_option = row['UNDERLYING_SYMBOL']
            options_lot_size_df = pd.read_csv(options_lot_size_csv)
            for(index, row) in options_lot_size_df.iterrows():
                if (row['SYMBOL'] == name_of_option):
                    lot_size = row['LOT_SIZE']
        else:
            logging.info(f"Error for {symbol}. not in input file")

    effective_profit_and_loss = trade_log_df.loc[trade_log_df['trade_entry_time'] == Time_trade_taken, 'Trade_exit_price'] - trade_log_df.loc[trade_log_df['trade_entry_time'] == Time_trade_taken, 'Trade_entry_price']
    logging.info(f"Lot size: for {symbol} is : {lot_size}") 
    effective_profit_and_loss = (effective_profit_and_loss * lot_size) - 60
    trade_log_df.loc[trade_log_df['trade_entry_time'] == Time_trade_taken, 'profit_loss'] = effective_profit_and_loss
    logging.info(f"Trade log : \n{trade_log_df}")
    # try:
    #     interval_suffix = "trade_log"
    #     output_filename = f"NIFTY_INDEX_{interval_suffix}_{to_date_str}.csv"
    #     output_path = os.path.join(output_dir_trade_log, output_filename)
    #     trade_log_df.to_csv(output_path, index=False)
    #     logging.info(f" Saved Trade log file: {output_filename}")
    # except Exception as e:
    #     logging.error(f" Error saving trade log for NIFTY_INDEX: {str(e)}")
    # Ensure the directory exists
    os.makedirs(output_dir_trade_log, exist_ok=True)
    output_path = os.path.join(output_dir_trade_log, output_filename)
    
    # If file exists, append; else, create new
    if os.path.exists(output_path):
        try:
            existing_df = pd.read_csv(output_path)
            # Concatenate and drop duplicates based on unique columns (adjust as needed)
            combined_df = pd.concat([existing_df, trade_log_df], ignore_index=True)
            combined_df.drop_duplicates(subset=["Instrument_name", "trade_entry_time"], keep='last', inplace=True)
            combined_df.to_csv(output_path, index=False)
        except Exception as e:
            print(f"    ✗ Error appending to trade log: {str(e)}")
            logging.error(f" Error appending to trade log: {str(e)}")
    else:
        trade_log_df.to_csv(output_path, index=False)

    return True
        
        
# Example usage
if __name__ == "__main__":

    # Initialize global variables
    options_df = pd.DataFrame()
    
    fetch_interval_seconds = 20 # Define the interval in seconds
    now = datetime.datetime.now()
    current_time = now.time()
    market_close = datetime.time(15, 29)
    market_start = datetime.time(9, 15)
    Option_type_to_trade = "None"
    Trade_symbol = "None"
    Ready_to_Trade = 'False'
    Option_1min_ready_for_trade_candle = {}
    Time_trade_taken = None

    trade_log_columns = [
        "Instrument_name",
        "trade_entry_time",
        "trade_exit_time",
        "Trade_entry_price",
        "Trade_exit_price",
        "Profit_n_loss"
    ]
    trade_log_df = pd.DataFrame(columns=trade_log_columns)

    # Create output directories if they don't exist
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(options_output_dir, exist_ok=True)

    # Initialize data stores and state variables
    data_store = {}  # In-memory store for dataframes: {interval: DataFrame}
    first_run = True
    last_15_min_fetch_minute = -1
    #options_processed = False  # Track if options have been processed in this session

    # Historical data variables
    df_1 = None
    df_5 = None
    df_60 = None
    df_daily = None

    logging.info("=== ****************************************************** ===")
    logging.info(f"=== ****************START LOGGING* ({now.strftime('%H:%M:%S')}) ************ ===")
    logging.info("=== ****************************************************** ===")

    print("=== Enhanced Historical Fetcher with Option Data Integration ===")
    print(f"Historical data output: {output_dir}")
    print(f"Options data output: {options_output_dir}")
    print(f"Options input file: {options_csv_file}")
    print("="*60)
    
    logging.info("=== Enhanced Historical Fetcher with Option Data Integration ===")
    logging.info(f"Historical data output: {output_dir}")
    logging.info(f"Options data output: {options_output_dir}")
    logging.info(f"Options input file: {options_csv_file}")
    logging.info("="*60)
    
    while True:
        now = datetime.datetime.now()
        current_time = now.time()

        # # === Option Data Processing (First Run Only) ===
        # #if first_run: #and not options_processed:
        # print("\n--- Processing Option Data ---")
        # try:
        #     options_results = process_options_csv()
        #     if options_results["success"]:
        #         print(f"✓ Options processing completed:")
        #         print(f"  - Total entries: {options_results['total_entries']}")
        #         print(f"  - Processed entries: {options_results['processed_entries']}")
        #         print(f"  - Successful fetches: {options_results['successful_fetches']}")
        #         print(f"  - Failed fetches: {options_results['failed_fetches']}")
        #     else:
        #         print(f"✗ Options processing failed: {options_results.get('error', 'Unknown error')}")
        #     #options_processed = True
        # except Exception as e:
        #     print(f"✗ Error during options processing: {str(e)}")
        #     #options_processed = True  # Mark as processed to avoid retry

        # === Historical Data Processing (Existing Functionality) ===
        print(f"\n--- Processing Historical Data ({now.strftime('%H:%M:%S')}) ---")
        logging.info(f"--- Processing Historical Data ({now.strftime('%H:%M:%S')}) ---")

        # Determine intervals to fetch
        intervals_to_fetch = set()
        if first_run:
            print("→ First run: scheduling all intervals for full historical fetch")
            logging.info(" First run: scheduling all intervals for full historical fetch")
            intervals_to_fetch.update(["1", "5", "60", "daily"])
            first_run = False
        else:
            intervals_to_fetch.update(["1", "5"])

        if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
            print(f"→ Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            logging.info(f" Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            intervals_to_fetch.add("60")
            last_15_min_fetch_minute = now.minute

        # Process historical data for each interval
        for interval in intervals_to_fetch:
            print(f"  → Fetching {interval}min data...")
            logging.info(f" Fetching {interval}min data...")
            df, from_date_str, to_date_str = fetch_historical_data(interval)
            print(f"  → After Fetching {interval}min data...",from_date_str, to_date_str)
            logging.info(f" After Fetching {interval}min data... {from_date_str} {to_date_str}")

            # Update Data Store
            if df is not None and not df.empty:
                print(f"    ✓ Fetched {len(df)} new records")
                logging.info(f" Fetched {len(df)} new records")
                if interval in data_store:
                    data_store[interval] = pd.concat([data_store[interval], df]).drop_duplicates(subset=['timestamp'], keep='last').sort_values(by='timestamp').reset_index(drop=True)
                else:
                    data_store[interval] = df.sort_values(by='timestamp').reset_index(drop=True)
                print(f"    → Data store for '{interval}' now has {len(data_store[interval])} total records")
                logging.info(f" Data store for '{interval}' now has {len(data_store[interval])} total records")

            # Process and save data
            if interval in data_store and not data_store[interval].empty:
                df_processed = process_dataframe(df)

                try:
                    interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                    output_filename = f"NIFTY_INDEX_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                    output_path = os.path.join(output_dir, output_filename)

                    df_processed.to_csv(output_path, index=False)
                    print(f"    ✓ Saved processed file: {output_filename}")
                    logging.info(f" Saved processed file: {output_filename}")
                except Exception as e:
                    print(f"    ✗ Error saving processed data for interval {interval}: {str(e)}")
                    logging.error(f" Error saving processed data for interval {interval}: {str(e)}")

                # Store processed dataframes for decision making
                if df is not None:
                    if interval == "1":
                        df_1 = df_processed
                    elif interval == "5":
                        df_5 = df_processed
                    elif interval == "60":
                        df_60 = df_processed
                    else:
                        df_daily = df_processed
            else:
                print(f"    ✗ No data available for {interval}min interval")
                logging.error(f" No data available for {interval}min interval")


        # for symbol in master_dataframes:
        #     print("Symbol_option type:\n",symbol.split()[-1])
        #     print("\nSymbol: ", symbol)
        #     print("master dataframes:\n",master_dataframes[symbol]['df_opt_1min'].head(2))

        # print("master dataframes:\n", master_dataframes['dataframes'])

        # === Market Hours Check ===
        #if (current_time < market_start) | (current_time > market_close):
        if (current_time > market_close):
            print(f"→ Market is closed (current time: {current_time}). Exiting...")
            logging.info(f"Market is closed (current time: {current_time}). Exiting...")
            break

        print("Ready_to_Trade ... First ",Ready_to_Trade)
        logging.info(f"Ready_to_Trade ... First {Ready_to_Trade}")
        #   Look for Trade Signals
        if Ready_to_Trade == 'False':
            Option_type_to_trade, Ready_to_Trade = look_for_trade_signals(df_1, df_5)
        
        print("Ready_to_Trade ... Second ",Ready_to_Trade)
        logging.info(f"Ready_to_Trade ... Second {Ready_to_Trade}")
        if Ready_to_Trade == 'True':
            # # === Option Data Processing (First Run Only) ===
            #if first_run: #and not options_processed:
            print("\n--- Processing Option Data ---")
            logging.info("--- Processing Option Data ---")
            try:
                options_results = process_options_csv()
                if options_results["success"]:
                    print(f"✓ Options processing completed:")
                    print(f"  - Total entries: {options_results['total_entries']}")
                    print(f"  - Processed entries: {options_results['processed_entries']}")
                    print(f"  - Successful fetches: {options_results['successful_fetches']}")
                    print(f"  - Failed fetches: {options_results['failed_fetches']}")
                    logging.info(f"Options processing completed:")
                    logging.info(f"  - Total entries: {options_results['total_entries']}")
                    logging.info(f"  - Processed entries: {options_results['processed_entries']}")
                    logging.info(f"  - Successful fetches: {options_results['successful_fetches']}")
                    logging.info(f"  - Failed fetches: {options_results['failed_fetches']}")
                else:
                    print(f"✗ Options processing failed: {options_results.get('error', 'Unknown error')}")
                    logging.error(f"Options processing failed: {options_results.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"✗ Error during options processing: {str(e)}")
                logging.error(f"Error during options processing: {str(e)}")
            master_dataframes = options_results['dataframes']
            #options_processed = True  # Mark as processed to avoid retry

            # for symbol in master_dataframes:
            #     print("Symbol_option type:\n",symbol.split()[-1])
            #     print("\nSymbol: ", symbol)
            #     print("master dataframes:\n",master_dataframes[symbol]['df_opt_1min'].head(2))
            #     result_1min_opt= find_latest_signals(master_dataframes[symbol]['df_opt_1min'])
            #     result_5min_opt= find_latest_signals(master_dataframes[symbol]['df_opt_5min'])
            #     result_60min_opt= find_latest_signals(master_dataframes[symbol]['df_opt_60min'])
            Trade_symbol, Option_1min_ready_for_trade_candle = Get_info_for_trade(master_dataframes, Option_type_to_trade)
            if Trade_symbol == "None":
                Ready_to_Trade = 'False'
                print("No ",Option_type_to_trade," Options chosen in input file. Nothing to trade")
                logging.info(f"No {Option_type_to_trade} Options chosen in input file. Nothing to trade")
            else:
                Ready_to_Trade = "Take trade"
        
        # trade_log_df = trade_log_df.append({
        #     "Instrument_name": Trade_symbol,
        #     "trade_entry_time": Time_trade_taken,
        #     "trade_exit_time": exit_time,  # set this variable when trade is exited
        #     "Trade_entry_price": entry_price,  # set this variable when trade is taken
        #     "Trade_exit_price": exit_price,    # set this variable when trade is exited
        #     "Profit_n_loss": pnl               # calculate P&L
        # }, ignore_index=True)
        
        print("Ready_to_Trade ... Third ",Ready_to_Trade)
        logging.info(f"Ready_to_Trade ... Third {Ready_to_Trade}")
        if Ready_to_Trade == "Take trade":
            # # === Option Data Processing (First Run Only) ===
            #if first_run: #and not options_processed:
            print("\n--- Processing Option Data ---")
            logging.info("--- Processing Option Data ---")
            try:
                options_results = process_options_csv()
                if options_results["success"]:
                    print(f"✓ Options processing completed:")
                    print(f"  - Total entries: {options_results['total_entries']}")
                    print(f"  - Processed entries: {options_results['processed_entries']}")
                    print(f"  - Successful fetches: {options_results['successful_fetches']}")
                    print(f"  - Failed fetches: {options_results['failed_fetches']}")
                    logging.info(f"Options processing completed:")
                    logging.info(f"  - Total entries: {options_results['total_entries']}")
                    logging.info(f"  - Processed entries: {options_results['processed_entries']}")
                    logging.info(f"  - Successful fetches: {options_results['successful_fetches']}")
                    logging.info(f"  - Failed fetches: {options_results['failed_fetches']}")
                else:
                    print(f"✗ Options processing failed: {options_results.get('error', 'Unknown error')}")
                    logging.error(f" Options processing failed: {options_results.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"✗ Error during options processing: {str(e)}")
                logging.error(f" Error during options processing: {str(e)}")
            master_dataframes = options_results['dataframes']
            #options_processed = True  # Mark as processed to avoid retry

            Time_trade_taken, Ready_to_Trade = take_the_trade(Trade_symbol, Option_1min_ready_for_trade_candle, master_dataframes)
            if(Ready_to_Trade == "Trade Taken"):
                new_row = {
                    "Instrument_name": Trade_symbol,
                    "trade_entry_time": Time_trade_taken,
                    "trade_exit_time": None,
                    "Trade_entry_price": master_dataframes[Trade_symbol]['df_opt_1min'].iloc[-1]['low'],
                    "Trade_exit_price": None,
                    "Profit_n_loss": None
                }
                trade_log_df = pd.concat([trade_log_df, pd.DataFrame([new_row])], ignore_index=True)

        
        print("Ready_to_Trade ... Fourth ",Ready_to_Trade)        
        logging.info(f"Ready_to_Trade ... Fourth {Ready_to_Trade}")
        if Ready_to_Trade == "Trade Taken":
            # # === Option Data Processing (First Run Only) ===
            #if first_run: #and not options_processed:
            print("\n--- Processing Option Data ---")
            logging.info("--- Processing Option Data ---")
            try:
                options_results = process_options_csv()
                if options_results["success"]:
                    print(f"✓ Options processing completed:")
                    print(f"  - Total entries: {options_results['total_entries']}")
                    print(f"  - Processed entries: {options_results['processed_entries']}")
                    print(f"  - Successful fetches: {options_results['successful_fetches']}")
                    print(f"  - Failed fetches: {options_results['failed_fetches']}")
                    logging.info(f" Options processing completed:")
                    logging.info(f"  - Total entries: {options_results['total_entries']}")
                    logging.info(f"  - Processed entries: {options_results['processed_entries']}")
                    logging.info(f"  - Successful fetches: {options_results['successful_fetches']}")
                    logging.info(f"  - Failed fetches: {options_results['failed_fetches']}")
                else:
                    print(f"✗ Options processing failed: {options_results.get('error', 'Unknown error')}")
                    logging.error(f" Options processing failed: {options_results.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"✗ Error during options processing: {str(e)}")
                logging.error(f" Error during options processing: {str(e)}")
            master_dataframes = options_results['dataframes']
            #options_processed = True  # Mark as processed to avoid retry

            Ready_to_Trade = check_for_trade_exit(df_1, df_5,Trade_symbol, Time_trade_taken, master_dataframes, trade_log_df)

        print("Ready_to_Trade ... Fifth ",Ready_to_Trade)
        logging.info(f"Ready_to_Trade ... Fifth {Ready_to_Trade}")
        if Ready_to_Trade == "Trade Exit":
            # # === Option Data Processing (First Run Only) ===
            #if first_run: #and not options_processed:
            print("\n--- Processing Option Data ---")
            logging.info("--- Processing Option Data ---")
            try:
                options_results = process_options_csv()
                if options_results["success"]:
                    print(f"✓ Options processing completed:")
                    print(f"  - Total entries: {options_results['total_entries']}")
                    print(f"  - Processed entries: {options_results['processed_entries']}")
                    print(f"  - Successful fetches: {options_results['successful_fetches']}")
                    print(f"  - Failed fetches: {options_results['failed_fetches']}")
                    logging.info(f" Options processing completed:")
                    logging.info(f"  - Total entries: {options_results['total_entries']}")
                    logging.info(f"  - Processed entries: {options_results['processed_entries']}")
                    logging.info(f"  - Successful fetches: {options_results['successful_fetches']}")
                    logging.info(f"  - Failed fetches: {options_results['failed_fetches']}")
                else:
                    print(f"✗ Options processing failed: {options_results.get('error', 'Unknown error')}")
                    logging.error(f" Options processing failed: {options_results.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"✗ Error during options processing: {str(e)}")
                logging.error(f" Error during options processing: {str(e)}")
            master_dataframes = options_results['dataframes']
            #options_processed = True  # Mark as processed to avoid retry

            if (exit_the_trade(Trade_symbol, Option_1min_ready_for_trade_candle, master_dataframes, trade_log_df)) is True:
                Ready_to_Trade = 'False'

        print("Ready_to_Trade ... Sixth ",Ready_to_Trade)
        logging.info(f"Ready_to_Trade ... Sixth {Ready_to_Trade}")

        # === Data Summary Display ===
        # print("\n--- Current Data Summary ---")
        # if df_1 is not None:
        #     print(f"1min data (last 2 rows):\n{df_1.tail(2)}")
        # if df_5 is not None:
        #     print(f"5min data (last 2 rows):\n{df_5.tail(2)}")
        # if df_60 is not None:
        #     print(f"60min data (last 2 rows):\n{df_60.tail(2)}")

        print(f"\n{'='*60}")
        print(f"Next update in {fetch_interval_seconds} seconds...")
        print(f"{'='*60}\n")
        logging.info(f"{'='*60}")
        logging.info(f"Next update in {fetch_interval_seconds} seconds...")
        logging.info(f"{'='*60}\n")

        # Wait for the specified interval
        time.sleep(fetch_interval_seconds)
    # try:
    #     interval_suffix = "trade_log"
    #     output_filename = f"NIFTY_INDEX_{interval_suffix}_{to_date_str}.csv"
    #     output_path = os.path.join(output_dir_trade_log, output_filename)
    #     trade_log_df.to_csv(output_path, index=False)
    #     print(f"    ✓ Saved Trade log file: {output_filename}")
    #     logging.info(f" Saved Trade log file: {output_filename}")
    # except Exception as e:
    #     print(f"    ✗ Error saving trade log for NIFTY_INDEX: {str(e)}")
    #     logging.error(f" Error saving trade log for NIFTY_INDEX: {str(e)}")

